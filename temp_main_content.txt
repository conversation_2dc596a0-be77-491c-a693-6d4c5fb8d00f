#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
メインスクリプト - 経済統計データの取得、分析、投稿を統合実行
"""

import os
import sys
import json
from datetime import datetime, timedelta
# from typing import List, Dict, Optional
import logging
import random


# 自作モジュールをインポート
# main.pyがプロジェクトルートにあるため、直接インポート
from src.data_collector import EStatDataCollector, load_indicators_config
from src.gemini_analyzer import GeminiAnalyzer
from src.twitter_poster import TwitterPoster
from src.news_collector import NewsCollector
from src.chart_generator import ChartGenerator

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class EconomicStatsBot:
    """経済統計分析・投稿ボットのメインクラス"""
    
    def __init__(self):
        """
        初期化
        
        `main.py`がプロジェクトルートにあることを考慮し、
        `.env`ファイルを読み込むように修正。
        """
        # 環境変数読み込み（キャッシュ対策で明示的に再読み込み）
        from dotenv import load_dotenv
        load_dotenv(override=True)
        
        # 各コンポーネントを初期化
        self.data_collector = None
        self.analyzer = None
        self.twitter_poster = None
        self.news_collector = None
        self.chart_generator = None
        
        # 履歴ファイルのパス
        self.history_file = "data/selection_history.json"
        
        # 履歴ディレクトリを作成
        os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
        
        logger.info("EconomicStatsBot initialized")
    
    def _initialize_components(self):
        """必要に応じてコンポーネントを初期化"""
        if self.data_collector is None:
            self.data_collector = EStatDataCollector()
        if self.analyzer is None:
            self.analyzer = GeminiAnalyzer()
        if self.twitter_poster is None:
            self.twitter_poster = TwitterPoster()
        if self.news_collector is None:
            self.news_collector = NewsCollector()
        if self.chart_generator is None:
            self.chart_generator = ChartGenerator()
    
    def collect_economic_data(self):
        """
        経済データを収集
        
        Returns:
            List[Dict]: 収集された経済データのリスト
        """
        self._initialize_components()
        
        logger.info("経済データ収集を開始")
        
        # 設定ファイルから指標を読み込み
        indicators = load_indicators_config()
        
        data_list = []
        
        for indicator in indicators:
            # 無効化された指標をスキップ
            if indicator.get('disabled', False):
                logger.info("無効化された指標をスキップ: %s", indicator['name'])
                continue
                
            try:
                logger.info("データ取得中: %s", indicator['name'])
                
                # データ取得
                data = self.data_collector.get_latest_data(
                    stats_data_id=indicator['stats_data_id'],
                    indicator_name=indicator['name'],
                    category=indicator.get('category', 'その他'),
                    importance=indicator.get('importance', 'medium')
                )
                
                if data and data.get('value') is not None:
                    data_list.append(data)
                    logger.info("✅ データ取得成功: %s = %s %s", 
                              indicator['name'], data['value'], data.get('unit', ''))
                else:
                    logger.warning("❌ データ取得失敗: %s", indicator['name'])
                    
            except Exception as e:
                logger.error("データ取得エラー (%s): %s", indicator['name'], str(e))
        
        logger.info("経済データ収集完了: %d件のデータを取得", len(data_list))
        return data_list
    
    def collect_news_data(self):
        """
        ニュースデータを収集
        
        Returns:
            List[Dict]: 収集されたニュースデータのリスト
        """
        self._initialize_components()
        
        logger.info("ニュースデータ収集を開始")
        
        try:
            news_list = self.news_collector.get_latest_news()
            logger.info("ニュースデータ収集完了: %d件のニュースを取得", len(news_list))
            return news_list
        except Exception as e:
            logger.error("ニュースデータ収集エラー: %s", str(e))
            return []
    
    def load_selection_history(self):
        """選択履歴を読み込み"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                    logger.info("選択履歴を読み込み: %d件", len(history.get('selections', [])))
                    return history
            else:
                logger.info("選択履歴ファイルが存在しません。新規作成します。")
                return {"selections": []}
        except Exception as e:
            logger.error("選択履歴読み込みエラー: %s", str(e))
            return {"selections": []}
    
    def save_selection_history(self, history):
        """選択履歴を保存"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            logger.info("選択履歴を保存しました")
        except Exception as e:
            logger.error("選択履歴保存エラー: %s", str(e))
    
    def update_selection_history(self, selected_indicators):
        """選択履歴を更新"""
        history = self.load_selection_history()
        
        # 現在の選択を履歴に追加
        current_selection = {
            "timestamp": datetime.now().isoformat(),
            "indicators": [
                {
                    "name": indicator['name'],
                    "category": indicator.get('category', 'その他'),
                    "stats_data_id": indicator.get('stats_data_id', ''),
                    "value": indicator.get('value'),
                    "unit": indicator.get('unit', '')
                }
                for indicator in selected_indicators
            ]
        }
        
        history["selections"].append(current_selection)
        
        # 履歴を5日間分のみ保持（120時間 = 5日）
        cutoff_time = datetime.now() - timedelta(hours=120)
        history["selections"] = [
            selection for selection in history["selections"]
            if datetime.fromisoformat(selection["timestamp"]) > cutoff_time
        ]
        
        self.save_selection_history(history)
        logger.info("選択履歴を更新: 現在の履歴件数 %d", len(history["selections"]))
    
    def get_recently_used_indicators(self):
        """最近使用された指標名のセットを取得"""
        history = self.load_selection_history()
        recently_used = set()
        
        for selection in history["selections"]:
            for indicator in selection["indicators"]:
                recently_used.add(indicator["name"])
        
        logger.info("最近使用された指標: %d種類", len(recently_used))
        return recently_used
    
    def get_recently_used_categories(self):
        """最近使用されたカテゴリのセットを取得"""
        history = self.load_selection_history()
        recently_used_categories = set()
        
        for selection in history["selections"]:
            for indicator in selection["indicators"]:
                recently_used_categories.add(indicator.get("category", "その他"))
        
        logger.info("最近使用されたカテゴリ: %d種類", len(recently_used_categories))
        return recently_used_categories
    
    def has_rich_time_series(self, data_item):
        """指標が豊富な時系列データを持つかチェック"""
        try:
            # stats_data_idから時系列データの豊富さを推定
            stats_id = data_item.get('stats_data_id', '')
            
            # 月次・四半期・年次データは時系列が豊富
            rich_patterns = [
                '0003',  # 基本的な経済統計
                '0004',  # 商業動態統計
                '0005',  # 工業統計
                '0006',  # 労働統計
                '0007',  # 貿易統計
            ]
            
            for pattern in rich_patterns:
                if stats_id.startswith(pattern):
                    return True
            
            return False
        except:
            return False
