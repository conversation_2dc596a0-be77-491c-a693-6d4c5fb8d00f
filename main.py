#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
メインスクリプト - 経済統計データの取得、分析、投稿を統合実行
"""

import os
import sys
import json
from datetime import datetime, timedelta
# from typing import List, Dict, Optional
import logging
import random


# 自作モジュールをインポート
# main.pyがプロジェクトルートにあるため、直接インポート
from src.data_collector import EStatDataCollector, load_indicators_config
from src.gemini_analyzer import GeminiAnalyzer
from src.twitter_poster import TwitterPoster
from src.news_collector import NewsCollector
from src.chart_generator import ChartGenerator

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class EconomicStatsBot:
    """経済統計分析・投稿ボットのメインクラス"""
    
    def __init__(self):
        """
        初期化
        
        `main.py`がプロジェクトルートにあることを考慮し、
        `.env`ファイルを読み込むように修正。
        """
        # 環境変数読み込み（キャッシュ対策で明示的に再読み込み）
        from dotenv import load_dotenv
        load_dotenv(override=True)
        
        # 各コンポーネントを初期化
        self.data_collector = None
        self.analyzer = None
        self.twitter_poster = None
        self.news_collector = None
        self.chart_generator = None
        
        self._initialize_components()
    
    def _initialize_components(self):
        """各コンポーネントを初期化"""
        try:
            # e-Stat APIキーチェック
            estat_api_key = os.getenv("ESTAT_API_KEY")
            if not estat_api_key:
                raise ValueError("ESTAT_API_KEYが設定されていません")

            # Gemini APIキーチェック
            gemini_api_key = os.getenv("GEMINI_API_KEY")
            if not gemini_api_key:
                raise ValueError("GEMINI_API_KEYが設定されていません")

            # Twitter API認証情報チェック
            twitter_keys = [
                os.getenv("TWITTER_API_KEY"),
                os.getenv("TWITTER_API_SECRET"),
                os.getenv("TWITTER_ACCESS_TOKEN"),
                os.getenv("TWITTER_ACCESS_TOKEN_SECRET")
            ]

            if not all(twitter_keys):
                raise ValueError("Twitter API認証情報が不足しています")

            # コンポーネント初期化
            self.data_collector = EStatDataCollector(estat_api_key)
            self.analyzer = GeminiAnalyzer(gemini_api_key)
            # 認証テストをスキップしてレート制限を回避
            self.twitter_poster = TwitterPoster(*twitter_keys, test_auth=False)
            self.news_collector = NewsCollector()
            self.chart_generator = ChartGenerator()

            # 分析で使用した関連ニュースを保存（動的ハッシュタグ生成用）
            self.current_relevant_news = []
            # 分析で使用した選択データを保存（グラフ作成用）
            self.current_selected_data = []

            # 選択履歴管理
            self.selection_history_file = "data/selection_history.json"
            self.max_history_days = 7  # 7日間の履歴を保持

            logger.info("全コンポーネントの初期化が完了しました")

        except Exception as e:
            logger.error("Component initialization error: %s", e)
            raise
    
    def collect_economic_data(self):
        """経済データを収集"""
        try:
            logger.info("経済データ収集を開始します")
            
            # 指標設定を読み込み
            indicators = load_indicators_config()

            if not indicators:
                raise ValueError("指標設定が読み込めませんでした")

            # 指標設定をクラス変数として保存（異常値修正で使用）
            self.indicators_config = indicators

            # 真のランダム化（時刻も含む）
            current_time = datetime.now()
            # 日付 + 時刻 + マイクロ秒 + プロセスIDでユニークなシード
            time_seed = int(current_time.strftime("%Y%m%d%H%M%S")) + current_time.microsecond + os.getpid()
            random.seed(time_seed)
            random.shuffle(indicators)
            logger.info("指標取得順序を真のランダム化 (シード: %s)", time_seed)

            # 各指標のデータを取得
            collected_data = []
            for i, indicator in enumerate(indicators):
                try:
                    # 無効化された指標をスキップ
                    if indicator.get('disabled', False):
                        logger.info("無効化された指標をスキップ (%d/%d): %s", i+1, len(indicators), indicator['name'])
                        continue

                    logger.info("データ取得中 (%d/%d): %s", i+1, len(indicators), indicator['name'])
                    data = self.data_collector.get_indicator_data(indicator)
                    if data:
                        collected_data.append(data)
                        logger.info("データ取得成功: %s", data['name'])
                    else:
                        logger.warning("データ取得失敗: %s", indicator['name'])
                except Exception as e:
                    logger.error("指標データ取得エラー (%s): %s", indicator['name'], e)
                    continue

            # シード値をリセット（他の処理への影響を避ける）
            random.seed()
            
            logger.info("データ収集完了: %d件の指標データを取得", len(collected_data))

            # データが取得できなかった場合の対処
            if len(collected_data) == 0:
                logger.warning("有効なデータが取得できませんでした。処理を継続します。")
                # 空のリストではなく、ダミーデータを返して処理を継続
                return []

            return collected_data

        except Exception as e:
            logger.error("経済データ収集エラー: %s", e)
            return []

    def load_selection_history(self):
        """選択履歴を読み込み"""
        try:
            # dataディレクトリが存在しない場合は作成
            os.makedirs("data", exist_ok=True)

            if os.path.exists(self.selection_history_file):
                with open(self.selection_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)

                # 古い履歴を削除（7日以上前）
                cutoff_date = (datetime.now() - timedelta(days=self.max_history_days)).strftime("%Y-%m-%d")
                history = {date: indicators for date, indicators in history.items() if date >= cutoff_date}

                return history
            else:
                return {}
        except Exception as e:
            logger.warning("選択履歴読み込みエラー: %s", e)
            return {}

    def save_selection_history(self, selected_indicators):
        """選択履歴を保存"""
        try:
            history = self.load_selection_history()
            today = datetime.now().strftime("%Y-%m-%d")
            history[today] = selected_indicators

            # dataディレクトリが存在しない場合は作成
            os.makedirs("data", exist_ok=True)

            with open(self.selection_history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

            logger.info("選択履歴を保存: %s - %s", today, selected_indicators)
        except Exception as e:
            logger.warning("選択履歴保存エラー: %s", e)

    def get_recently_used_indicators(self):
        """最近使用された指標のリストを取得（強化版）"""
        try:
            history = self.load_selection_history()
            recently_used = []

            # 過去5日間の使用履歴を取得（より長期間の多様性確保）
            for i in range(5):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                if date in history:
                    recently_used.extend(history[date])

            return list(set(recently_used))  # 重複を除去
        except Exception as e:
            logger.warning("最近使用指標取得エラー: %s", e)
            return []

    def get_recently_used_categories(self):
        """最近使用されたカテゴリのリストを取得"""
        try:
            history = self.load_selection_history()
            recently_used_indicators = []

            # 過去3日間の使用履歴を取得
            for i in range(3):
                date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                if date in history:
                    recently_used_indicators.extend(history[date])

            # 指標名からカテゴリを取得
            indicators = load_indicators_config()
            indicator_categories = {ind['name']: ind.get('category', 'その他') for ind in indicators}

            recently_used_categories = []
            for indicator_name in recently_used_indicators:
                if indicator_name in indicator_categories:
                    recently_used_categories.append(indicator_categories[indicator_name])

            return list(set(recently_used_categories))  # 重複を除去
        except Exception as e:
            logger.warning("最近使用カテゴリ取得エラー: %s", e)
            return []

    def convert_numpy_types_in_data_list(self, data_list):
        """data_list内のNumPy型を標準Python型に変換"""
        try:
            if not data_list:
                return

            for item in data_list:
                if not item or not isinstance(item, dict):
                    continue

                # 基本値の変換
                if 'value' in item and hasattr(item['value'], 'item'):
                    item['value'] = item['value'].item()

                # 時系列分析データの変換
                if 'time_series_analysis' in item and item['time_series_analysis']:
                    ts_analysis = item['time_series_analysis']
                    for key in ['change_rate', 'yearly_change_rate', 'latest_value', 'previous_value']:
                        if key in ts_analysis and hasattr(ts_analysis[key], 'item'):
                            ts_analysis[key] = ts_analysis[key].item()

                # 外れ値分析データの変換
                if 'outlier_analysis' in item and item['outlier_analysis'] and 'outlier_info' in item['outlier_analysis']:
                    outlier_info = item['outlier_analysis']['outlier_info']
                    if 'latest_value' in outlier_info and hasattr(outlier_info['latest_value'], 'item'):
                        outlier_info['latest_value'] = outlier_info['latest_value'].item()
                    if 'statistical_bounds' in outlier_info and isinstance(outlier_info['statistical_bounds'], list):
                        outlier_info['statistical_bounds'] = [
                            bound.item() if hasattr(bound, 'item') else bound
                            for bound in outlier_info['statistical_bounds']
                        ]

                # 履歴データの変換
                if 'historical_data' in item and isinstance(item['historical_data'], list):
                    for hist_item in item['historical_data']:
                        if isinstance(hist_item, dict) and '$' in hist_item:
                            if hasattr(hist_item['$'], 'item'):
                                hist_item['$'] = hist_item['$'].item()

            logger.debug("NumPy型変換完了: %d件のアイテムを処理", len(data_list))

        except Exception as e:
            logger.warning("NumPy型変換エラー: %s", e)

    def filter_outliers_and_select_indicators(self, data_list):
        """異常値を除外し、日替わりで多様な指標を選択"""
        try:
            from datetime import datetime

            logger.info("異常値フィルタリングと指標選択を開始 (対象: %d件)", len(data_list) if data_list else 0)

            # データの事前検証
            if not data_list:
                logger.error("data_listが空またはNoneです")
                return {
                    'selected_data': [],
                    'outlier_data': [],
                    'total_indicators': 0,
                    'valid_indicators': 0,
                    'selection_reason': 'no_input_data'
                }

            # 異常値を除外
            valid_data = []
            outlier_data = []
            
            for i, data in enumerate(data_list):
                logger.debug("処理中のデータ[{i}]: {type(data)} - %s", data)

                if data is None:
                    logger.warning("Noneデータをスキップしました (インデックス: %s)", i)
                    continue

                if not isinstance(data, dict):
                    logger.warning("辞書型でないデータをスキップしました (インデックス: {i}, 型: %s)", type(data))
                    continue

                try:
                    outlier_analysis = data.get('outlier_analysis', {})
                    original_value = data.get('value')
                except AttributeError as e:
                    logger.error("データ[{i}]でAttributeError: {e}, データ型: {type(data)}, データ内容: %s", data)
                    continue

                # 極端な異常値の事前チェック（強化版）
                is_extreme_outlier = False

                # データ品質の事前チェック（強化版）
                if original_value is None or str(original_value).upper() in ['N/A', 'NAN', 'NULL', '', 'NAT']:
                    is_extreme_outlier = True
                    logger.warning("無効データ検出: %s = %s", data.get('name', ''), original_value)
                elif data.get('time') in [None, 'N/A', 'NaT', '', 'NAT', 'nat']:
                    is_extreme_outlier = True
                    logger.warning("無効時点データ検出: %s 時点 = %s", data.get('name', ''), data.get('time'))
                # 時点データの文字列チェック
                elif isinstance(data.get('time'), str) and ('nat' in data.get('time', '').lower() or 'n/a' in data.get('time', '').lower()):
                    is_extreme_outlier = True
                    logger.warning("無効時点文字列検出: %s 時点 = %s", data.get('name', ''), data.get('time'))
                elif original_value is not None:
                    indicator_name = data.get('name', '')

                    # NumPy型を標準Python型に変換
                    if hasattr(original_value, 'item'):  # NumPy型の場合
                        original_value = original_value.item()
                    elif hasattr(original_value, 'dtype'):  # NumPy配列の場合
                        original_value = float(original_value)

                    # 失業率の異常値チェック（日本の失業率は通常2-4%、絶対上限6%）
                    if "失業率" in indicator_name or "完全失業率" in indicator_name:
                        if original_value > 6.0 or original_value < 1.0:  # 6%以上または1%未満は明らかに異常
                            is_extreme_outlier = True
                            logger.warning("失業率異常値検出: %.1f%% (正常範囲: 1.0-6.0%%) - 即座に除外", original_value)
                        elif original_value > 10.0:  # 10%以上は極端な異常値
                            is_extreme_outlier = True
                            logger.error("失業率極端異常値検出: %.1f%% - データエラーの可能性", original_value)

                    # 機械受注の異常値チェック（月次で5000億円～2兆円が現実的）
                    elif "機械受注" in indicator_name:
                        if original_value > 20000:  # 2兆円以上は異常
                            is_extreme_outlier = True
                            logger.warning("機械受注異常値検出: %.0f億円 (上限: 2兆円) - 即座に除外", original_value)
                        elif original_value < 5000 and original_value > 100:  # 5000億円未満は異常
                            is_extreme_outlier = True
                            logger.warning("機械受注異常値検出: %.0f億円 (下限: 5000億円) - 即座に除外", original_value)

                    # 輸出入額の異常値チェック（月次で3兆円～10兆円が現実的）
                    elif "輸出" in indicator_name or "輸入" in indicator_name:
                        if original_value > 100000:  # 10兆円以上は異常（億円単位）
                            is_extreme_outlier = True
                            logger.warning("貿易額異常値検出: %.0f億円 (上限: 10兆円) - 即座に除外", original_value)
                        elif original_value < 30000 and original_value > 1000:  # 3兆円未満は異常
                            is_extreme_outlier = True
                            logger.warning("貿易額異常値検出: %.0f億円 (下限: 3兆円) - 即座に除外", original_value)

                    # 機械受注の異常値チェック（月次で5000億円～2兆円が現実的）
                    elif "機械受注" in indicator_name:
                        if original_value > 20000:  # 2兆円以上は異常
                            is_extreme_outlier = True
                            logger.warning("機械受注異常値検出: %.0f億円 (上限: 2兆円) - 即座に除外", original_value)
                        elif original_value < 5000 and original_value > 100:  # 5000億円未満は異常
                            is_extreme_outlier = True
                            logger.warning("機械受注異常値検出: %.0f億円 (下限: 5000億円) - 即座に除外", original_value)

                    # 輸出入額の異常値チェック（月次で3兆円～10兆円が現実的）
                    elif "輸出" in indicator_name or "輸入" in indicator_name:
                        if original_value > 100000:  # 10兆円以上は異常（億円単位）
                            is_extreme_outlier = True
                            logger.warning("貿易額異常値検出: %.0f億円 (上限: 10兆円) - 即座に除外", original_value)
                        elif original_value < 30000 and original_value > 1000:  # 3兆円未満は異常
                            is_extreme_outlier = True
                            logger.warning("貿易額異常値検出: %.0f億円 (下限: 3兆円) - 即座に除外", original_value)

                    # 有効求人倍率の異常値チェック（厳格化：通常1.0-1.5倍、絶対上限1.8倍）
                    elif "有効求人倍率" in indicator_name:
                        if original_value > 1.8 or original_value < 0.3:  # 1.8倍以上または0.3倍未満は明らかに異常
                            is_extreme_outlier = True
                            logger.warning("有効求人倍率異常値検出: %.2f倍 (正常範囲: 0.3-1.8倍) - 即座に除外", original_value)
                        elif original_value > 10:  # 10倍以上は極端な異常値
                            is_extreme_outlier = True
                            logger.error("有効求人倍率極端異常値検出: %.2f倍 - データエラーの可能性", original_value)

                    # 鉱工業指数・生産指数・出荷指数の異常値チェック（通常80-120）
                    elif (("鉱工業" in indicator_name or "生産指数" in indicator_name or
                           "出荷指数" in indicator_name) and "指数" in indicator_name):
                        if original_value > 200 or original_value < 50:  # 200以上または50未満は明らかに異常
                            is_extreme_outlier = True
                            logger.warning("生産指数系異常値検出: %.1f (正常範囲: 50-200)", original_value)

                    # 第三次産業活動指数の異常値チェック（通常95-110）
                    elif "第三次産業活動指数" in indicator_name:
                        if original_value > 150 or original_value < 80:  # 150以上または80未満は明らかに異常
                            is_extreme_outlier = True
                            logger.warning("第三次産業活動指数異常値検出: %.1f (正常範囲: 80-150)", original_value)
                        elif original_value > 1000:  # 1000以上は極端な異常値（18110.0など）
                            is_extreme_outlier = True
                            logger.error("第三次産業活動指数極端異常値検出: %.1f - データエラーの可能性", original_value)

                    # 物価指数の異常値チェック（通常90-120、2020年=100基準）
                    elif "物価指数" in indicator_name:
                        if original_value < 50 or original_value > 200:  # 50未満または200以上は明らかに異常
                            is_extreme_outlier = True
                            logger.warning("物価指数異常値検出: %.1f (正常範囲: 50-200)", original_value)
                        elif original_value < 10:  # 10未満は単位間違いの可能性が高い
                            is_extreme_outlier = True
                            logger.warning("物価指数極端異常値検出: %.1f (10未満は単位間違いの可能性)", original_value)

                    # 一般的な極端値チェック（全指標共通）
                    elif original_value > 1000000 or original_value < -1000000:  # 100万以上または-100万未満
                        is_extreme_outlier = True
                        logger.warning("極端値検出: %s = %.1f (範囲外)", indicator_name, original_value)

                if outlier_analysis.get('has_outliers', False) or is_extreme_outlier:
                    severity = outlier_analysis.get('outlier_info', {}).get('severity', 'low')
                    reasons = outlier_analysis.get('outlier_info', {}).get('reasons', [])

                    if is_extreme_outlier:
                        reasons.append("極端な非現実的値")
                        severity = 'high'

                    # 極端な異常値は修正を試行せずに即座に除外
                    if is_extreme_outlier:
                        outlier_data.append(data)
                        logger.warning("極端異常値により即座に除外: %s = %s (理由: %s)", data['name'], original_value, ', '.join(reasons))
                        continue

                # 時系列データの品質チェック（トレンド分析のため）
                historical_data = data.get('historical_data', [])
                if len(historical_data) >= 3:
                    # 時系列データ内の異常値もチェック
                    clean_historical_data = self._clean_historical_data(historical_data, data['name'])
                    data['historical_data'] = clean_historical_data
                    logger.debug("時系列データクリーニング完了: %s (%d→%d件)", data['name'], len(historical_data), len(clean_historical_data))

                    # 通常の異常値の推定修正を試行
                    if original_value is not None and not is_extreme_outlier:
                        # 指標設定を取得（推定修正用）
                        indicator_config = None
                        if hasattr(self, 'indicators_config') and self.indicators_config:
                            for indicator in self.indicators_config:
                                if indicator['name'] == data['name']:
                                    indicator_config = indicator
                                    break

                        if indicator_config:
                            corrected_value = self.data_collector.estimate_correct_value(original_value, indicator_config)

                            if corrected_value is not None:
                                # 修正値の妥当性を再チェック
                                is_corrected_valid = self._validate_corrected_value(corrected_value, data['name'])

                                if is_corrected_valid:
                                    # 修正値を適用
                                    data['value'] = corrected_value
                                    data['corrected_from_outlier'] = True
                                    data['original_value'] = original_value
                                    logger.info("異常値修正適用: %s = %s → %s", data['name'], original_value, corrected_value)
                                    valid_data.append(data)
                                    continue
                                else:
                                    # 修正値も異常な場合は除外
                                    outlier_data.append(data)
                                    logger.warning("修正値も異常により除外: %s = %s → %s", data['name'], original_value, corrected_value)
                                    continue
                            else:
                                # 修正できない異常値は必ず除外
                                outlier_data.append(data)
                                logger.warning("修正不可能な異常値により除外: %s = %s", data['name'], original_value)
                                continue

                    # 修正できない場合は除外判定
                    should_exclude = False

                    # 重大な異常値は必ず除外
                    if severity == 'high':
                        should_exclude = True

                    # 特定の異常パターンも除外
                    for reason in reasons:
                        if any(keyword in reason for keyword in ["異常な下落率", "極端な変化率", "率指標で0以下", "異常値", "極端な非現実的値"]):
                            should_exclude = True
                            break

                    if should_exclude:
                        outlier_data.append(data)
                        logger.warning("異常値により除外: %s = %s (理由: %s)", data['name'], data['value'], ', '.join(reasons))
                        continue

                valid_data.append(data)

            logger.info("データフィルタリング完了: 有効データ %d件, 異常値 %d件", len(valid_data), len(outlier_data))

            # 有効データが少ない場合の警告と詳細ログ
            if len(valid_data) < 2:
                logger.warning("有効データが少なすぎます: %s件", len(valid_data))
                if len(outlier_data) > 0:
                    logger.warning("多数の異常値が検出されました:")
                    for outlier in outlier_data:
                        outlier_analysis = outlier.get('outlier_analysis', {})
                        reasons = outlier_analysis.get('outlier_info', {}).get('reasons', ['不明'])
                        logger.warning("  除外: %s = %s (理由: %s)", outlier['name'], outlier['value'], ', '.join(reasons))

                # 代替指標選択を試行
                logger.info("代替指標選択を試行します...")
                additional_data = self._get_additional_indicators(len(valid_data))
                if additional_data:
                    valid_data.extend(additional_data)
                    logger.info("代替指標を追加: %d件 → %d件", len(valid_data) - len(additional_data), len(valid_data))
                else:
                    logger.warning("代替指標の取得に失敗しました。")

                # 異常値を含めずに、より多くの指標を取得することを推奨
                logger.warning("データ品質に問題があります。異常値は分析に含めません。")

            # フォールバック処理: 有効指標が少ない場合は代替指標を取得（強化版）
            if len(valid_data) < 5:  # 閾値を3から5に上げて多様性を確保
                logger.info("代替指標選択を試行します... (現在: %d件)", len(valid_data))
                additional_data = self.get_additional_indicators(valid_data)
                if additional_data:
                    valid_data.extend(additional_data)
                    logger.info("代替指標追加後: %d件", len(valid_data))

                # さらに不足している場合は、より緩い条件で追加取得
                if len(valid_data) < 3:
                    logger.info("緊急フォールバック: より多くの代替指標を取得")
                    emergency_data = self.get_emergency_fallback_indicators()
                    if emergency_data:
                        valid_data.extend(emergency_data)
                        logger.info("緊急フォールバック後: %d件", len(valid_data))

            if not valid_data:
                logger.error("有効なデータが取得できませんでした。全て異常値として除外されました。")
                return {
                    'selected_data': [],
                    'outlier_data': outlier_data,
                    'total_indicators': len(data_list),
                    'valid_indicators': 0,
                    'daily_seed': 0,
                    'selection_reason': 'no_valid_data'
                }
            
            # 最近使用された指標とカテゴリを取得
            recently_used = self.get_recently_used_indicators()
            recently_used_categories = self.get_recently_used_categories()
            logger.info("最近使用された指標 (過去5日): %s", recently_used)
            logger.info("最近使用されたカテゴリ (過去3日): %s", recently_used_categories)

            # 強化されたランダム選択（時刻+日付+マイクロ秒+プロセスID+分散要素）
            current_time = datetime.now()
            # より複雑なシード生成で真のランダム性を確保
            time_component = int(current_time.strftime("%Y%m%d%H%M%S"))
            micro_component = current_time.microsecond
            data_component = len(valid_data) * hash(str([item['name'] for item in valid_data])) % 10000

            # さらなる分散要素を追加
            import os
            process_id = os.getpid()
            random_factor = hash(str(current_time.timestamp())) % 10000

            selection_seed = time_component + micro_component + data_component + process_id + random_factor
            random.seed(selection_seed)
            logger.info("指標選択ランダム化 (強化シード: %d, 時刻: %s)", selection_seed, current_time.strftime('%Y-%m-%d %H:%M:%S'))

            # カテゴリ別に分類
            categories = {}
            for data in valid_data:
                category = data.get('category', 'その他')
                if category not in categories:
                    categories[category] = []
                categories[category].append(data)

            logger.info("利用可能なカテゴリ: %s", list(categories.keys()))

            # 複数指標選択（多様性重視の時系列分析）
            selected_data = []
            max_indicators = min(3, len(valid_data))  # 最大3つの指標を選択

            # 新鮮な指標と使用済み指標を分離
            fresh_items = [item for item in valid_data if item['name'] not in recently_used]
            used_items = [item for item in valid_data if item['name'] in recently_used]

            # 新鮮なカテゴリと使用済みカテゴリを分離
            fresh_category_items = [item for item in valid_data if item.get('category', 'その他') not in recently_used_categories]
            used_category_items = [item for item in valid_data if item.get('category', 'その他') in recently_used_categories]

            logger.info("新鮮な指標: %d件, 使用済み指標: %d件", len(fresh_items), len(used_items))
            logger.info("新鮮なカテゴリの指標: %d件, 使用済みカテゴリの指標: %d件", len(fresh_category_items), len(used_category_items))

            # 重要度による重み付けを設定
            importance_weights = {'high': 3, 'medium': 2, 'low': 1}

            # 時系列データが豊富な指標を優先選択
            def has_rich_time_series(item):
                """時系列データが豊富かどうかを判定"""
                historical_data = item.get('historical_data', [])
                return len(historical_data) >= 6  # 半年分以上のデータがある（3年分データから十分な時系列分析が可能）

            # 多様性を重視した優先順位付け
            # 1. 新鮮な指標 + 新鮮なカテゴリ（最高優先度）
            fresh_indicator_fresh_category = [item for item in fresh_items if item.get('category', 'その他') not in recently_used_categories]

            # 2. 新鮮な指標 + 使用済みカテゴリ
            fresh_indicator_used_category = [item for item in fresh_items if item.get('category', 'その他') in recently_used_categories]

            # 3. 使用済み指標 + 新鮮なカテゴリ
            used_indicator_fresh_category = [item for item in used_items if item.get('category', 'その他') not in recently_used_categories]

            # 4. 使用済み指標 + 使用済みカテゴリ（最低優先度）
            used_indicator_used_category = [item for item in used_items if item.get('category', 'その他') in recently_used_categories]

            # 各グループ内で時系列データの豊富さによってさらに分類
            def categorize_by_time_series(items):
                rich = [item for item in items if has_rich_time_series(item)]
                limited = [item for item in items if not has_rich_time_series(item)]
                return rich, limited

            # 最終的な優先順位リスト（多様性重視）
            prioritized_groups = []

            # グループ1: 新鮮指標 + 新鮮カテゴリ
            rich_1, limited_1 = categorize_by_time_series(fresh_indicator_fresh_category)
            prioritized_groups.extend([rich_1, limited_1])

            # グループ2: 新鮮指標 + 使用済みカテゴリ
            rich_2, limited_2 = categorize_by_time_series(fresh_indicator_used_category)
            prioritized_groups.extend([rich_2, limited_2])

            # グループ3: 使用済み指標 + 新鮮カテゴリ
            rich_3, limited_3 = categorize_by_time_series(used_indicator_fresh_category)
            prioritized_groups.extend([rich_3, limited_3])

            # グループ4: 使用済み指標 + 使用済みカテゴリ
            rich_4, limited_4 = categorize_by_time_series(used_indicator_used_category)
            prioritized_groups.extend([rich_4, limited_4])

            # 優先順位付きアイテムリストを作成（グループ内でランダム化）
            prioritized_items = []
            for group in prioritized_groups:
                # 各グループ内でランダムシャッフル
                group_copy = group.copy()
                random.shuffle(group_copy)
                prioritized_items.extend(group_copy)

            logger.info("多様性重視の優先順位: 新鮮指標+新鮮カテゴリ(%d) > 新鮮指標+使用済みカテゴリ(%d) > 使用済み指標+新鮮カテゴリ(%d) > 使用済み指標+使用済みカテゴリ(%d)",
                       len(fresh_indicator_fresh_category), len(fresh_indicator_used_category),
                       len(used_indicator_fresh_category), len(used_indicator_used_category))

            if prioritized_items:
                # 強化された重み付けによる選択（多様性重視）
                weighted_items = []
                for item in prioritized_items:
                    # 基本重み（重要度）
                    weight = importance_weights.get(item.get('importance', 'medium'), 2)

                    # 時系列データが豊富な場合は重みを追加
                    if has_rich_time_series(item):
                        weight += 2

                    # 新鮮な指標の場合は重みを大幅追加（多様性重視）
                    if item in fresh_items:
                        weight += 3  # 1から3に増加

                    # 新鮮なカテゴリの場合はさらに重みを追加（カテゴリ多様性重視）
                    if item.get('category', 'その他') not in recently_used_categories:
                        weight += 2

                    # 最高優先度グループ（新鮮指標+新鮮カテゴリ）には特別ボーナス
                    if (item in fresh_items and
                        item.get('category', 'その他') not in recently_used_categories):
                        weight += 3  # 特別ボーナス

                    # 重み付きアイテムを追加
                    weighted_items.extend([item] * max(1, weight))  # 最低1つは保証

                # 重み付きアイテムリストをシャッフル（さらなるランダム化）
                random.shuffle(weighted_items)

                # 複数指標を多様性重視で選択
                if weighted_items:
                    selected_items = []
                    selected_categories = set()
                    attempts = 0
                    max_attempts = len(weighted_items) * 2  # 無限ループ防止

                    while len(selected_items) < max_indicators and attempts < max_attempts:
                        attempts += 1
                        chosen_item = random.choice(weighted_items)

                        # 既に選択済みの指標は除外
                        if chosen_item['name'] in [item['name'] for item in selected_items]:
                            continue

                        # カテゴリの多様性を確保（同じカテゴリは最大1つまで）
                        item_category = chosen_item.get('category', 'その他')
                        if len(selected_items) > 0 and item_category in selected_categories:
                            # 50%の確率でスキップ（完全に除外しない）
                            if random.random() < 0.5:
                                continue

                        selected_items.append(chosen_item)
                        selected_categories.add(item_category)

                        # 選択理由の詳細ログ
                        freshness = "新規" if chosen_item in fresh_items else "再利用"
                        category_freshness = "新規カテゴリ" if chosen_item.get('category', 'その他') not in recently_used_categories else "使用済みカテゴリ"
                        time_series_quality = "豊富" if has_rich_time_series(chosen_item) else "限定"
                        importance = chosen_item.get('importance', 'medium')

                        logger.info("✅ 選択された指標[%d]: %s", len(selected_items), chosen_item['name'])
                        logger.info("   - 指標新鮮度: %s", freshness)
                        logger.info("   - カテゴリ新鮮度: %s (%s)", category_freshness, chosen_item.get('category', 'その他'))
                        logger.info("   - 時系列品質: %s", time_series_quality)
                        logger.info("   - 重要度: %s", importance)

                    selected_data = selected_items

                    if not selected_data:
                        # フォールバック: 最初の有効な指標を選択
                        selected_data = [valid_data[0]]
                        logger.warning("フォールバック選択: %s", valid_data[0]['name'])
                else:
                    # フォールバック: 最初の有効な指標を選択
                    selected_data = [valid_data[0]]
                    logger.warning("フォールバック選択: %s", valid_data[0]['name'])
            else:
                logger.error("選択可能な指標がありません")
                return {"selected_data": [], "outlier_data": outlier_data}
            
            selected_names = [data['name'] for data in selected_data]
            logger.info("最終選択された指標: %s", ', '.join(selected_names))

            # 選択履歴を保存
            self.save_selection_history(selected_names)

            # 多様性統計をログ出力（強化版）
            fresh_count = sum(1 for name in selected_names if name not in recently_used)
            fresh_category_count = sum(1 for data in selected_data
                                     if data.get('category', 'その他') not in recently_used_categories)

            logger.info("📊 多様性統計:")
            logger.info("   - 新規指標: %d/%d件", fresh_count, len(selected_names))
            logger.info("   - 新規カテゴリ: %d/%d件", fresh_category_count, len(selected_names))
            logger.info("   - 選択シード: %d", selection_seed)

            # 選択された指標のカテゴリ情報
            selected_categories = [data.get('category', 'その他') for data in selected_data]
            logger.info("   - 選択カテゴリ: %s", ', '.join(selected_categories))

            # シード値をリセット（他の処理への影響を避ける）
            random.seed()
            
            return {
                'selected_data': selected_data,
                'outlier_data': outlier_data,
                'total_indicators': len(data_list),
                'valid_indicators': len(valid_data),
                'selection_seed': selection_seed,
                'selection_reason': 'time_based_random_selection_with_outlier_filtering'
            }
            
        except Exception as e:
            logger.error("指標選択エラー: %s", e)
            logger.error("エラー詳細: %s: %s", type(e).__name__, str(e))
            logger.error("data_list型: %s, 長さ: %s", type(data_list), len(data_list) if data_list else 'None')
            if data_list and len(data_list) > 0:
                try:
                    # 安全にログ出力（NumPy型変換後）
                    first_item = data_list[0]
                    if isinstance(first_item, dict):
                        safe_item = {k: str(v) for k, v in first_item.items()}
                        logger.error("data_list最初の要素: %s", safe_item)
                    else:
                        logger.error("data_list最初の要素: %s", str(first_item))
                except Exception as log_error:
                    logger.error("data_list最初の要素ログ出力エラー: %s", log_error)

            # 改善されたフォールバック：異常値除外後のランダム選択
            if data_list and len(data_list) > 0:
                try:
                    # フォールバック処理でも異常値を除外
                    valid_fallback_data = []
                    for data in data_list:
                        original_value = data.get('value')
                        indicator_name = data.get('name', '')

                        # NumPy型を標準Python型に変換
                        if hasattr(original_value, 'item'):  # NumPy型の場合
                            original_value = original_value.item()
                        elif hasattr(original_value, 'dtype'):  # NumPy配列の場合
                            original_value = float(original_value)

                        # 異常値チェック
                        is_extreme_outlier = False

                        # 失業率の異常値チェック（日本の失業率は通常2-4%、絶対上限6%）
                        if "失業率" in indicator_name or "完全失業率" in indicator_name:
                            if original_value > 6.0 or original_value < 1.0:
                                is_extreme_outlier = True
                                logger.warning("フォールバック処理で失業率異常値除外: %.1f%% (正常範囲: 1.0-6.0%%)", original_value)

                        # 有効求人倍率の異常値チェック
                        elif "有効求人倍率" in indicator_name:
                            if original_value > 3.0 or original_value < 0.3:
                                is_extreme_outlier = True
                                logger.warning("フォールバック処理で有効求人倍率異常値除外: %.2f倍 (正常範囲: 0.3-3.0倍)", original_value)

                        # 機械受注の異常値チェック
                        elif "機械受注" in indicator_name:
                            if original_value > 20000 or (original_value < 5000 and original_value > 100):
                                is_extreme_outlier = True
                                logger.warning("フォールバック処理で機械受注異常値除外: %.0f億円 (正常範囲: 5000-20000億円)", original_value)

                        # 輸出入額の異常値チェック
                        elif "輸出" in indicator_name or "輸入" in indicator_name:
                            if original_value > 100000 or (original_value < 30000 and original_value > 1000):
                                is_extreme_outlier = True
                                logger.warning("フォールバック処理で貿易額異常値除外: %.0f億円 (正常範囲: 30000-100000億円)", original_value)

                        # 生産指数系の異常値チェック
                        elif any(keyword in indicator_name for keyword in ['生産指数', '在庫指数', '出荷指数']):
                            if original_value > 200 or original_value < 50:
                                is_extreme_outlier = True
                                logger.warning("フォールバック処理で生産指数系異常値除外: %.1f (正常範囲: 50-200)", original_value)

                        # 物価指数の異常値チェック
                        elif "物価指数" in indicator_name:
                            if original_value > 200 or original_value < 50:
                                is_extreme_outlier = True
                                logger.warning("フォールバック処理で物価指数異常値除外: %.1f (正常範囲: 50-200)", original_value)

                        # 第三次産業活動指数の異常値チェック（通常95-110）
                        elif "第三次産業活動指数" in indicator_name:
                            if original_value > 150 or original_value < 80:
                                is_extreme_outlier = True
                                logger.warning("フォールバック処理で第三次産業活動指数異常値除外: %.1f (正常範囲: 80-150)", original_value)
                            elif original_value > 1000:  # 1000以上は極端な異常値（18110.0など）
                                is_extreme_outlier = True
                                logger.error("フォールバック処理で第三次産業活動指数極端異常値除外: %.1f - データエラーの可能性", original_value)

                        if not is_extreme_outlier:
                            valid_fallback_data.append(data)

                    if not valid_fallback_data:
                        logger.error("フォールバック処理でも有効な指標がありません")
                        return {"selected_data": [], "outlier_data": data_list}

                    # フォールバック用の独立したランダムシード
                    fallback_time = datetime.now()
                    fallback_seed = int(fallback_time.strftime("%Y%m%d%H%M%S")) + fallback_time.microsecond + hash(str(e)) % 10000
                    random.seed(fallback_seed)

                    # 有効な指標からランダムに選択
                    available_count = len(valid_fallback_data)
                    select_count = min(3, available_count)

                    # ランダムサンプリング
                    if select_count == available_count:
                        selected = valid_fallback_data.copy()
                    else:
                        selected = random.sample(valid_fallback_data, select_count)

                    logger.warning("フォールバック処理: %d件の有効指標から%d件をランダム選択 (シード: %s)", available_count, len(selected), fallback_seed)
                    logger.info("フォールバック選択指標: %s", [data.get('name', 'Unknown') for data in selected])

                    # シード値をリセット
                    random.seed()

                    return {
                        'selected_data': selected,
                        'outlier_data': [],
                        'total_indicators': len(data_list),
                        'valid_indicators': len(data_list),
                        'fallback_seed': fallback_seed,
                        'selection_reason': 'fallback_random_selection'
                    }
                except Exception as fallback_error:
                    logger.error("フォールバック処理でもエラー: %s", fallback_error)
                    # 最終フォールバック: 最初の指標のみ
                    selected = [data_list[0]]
                    logger.warning("Warning occurred")
                    return {
                        'selected_data': selected,
                        'outlier_data': [],
                        'total_indicators': len(data_list),
                        'valid_indicators': len(data_list),
                        'selection_reason': 'final_fallback_single'
                    }
            else:
                logger.error("data_listが空またはNoneのため、フォールバック処理も失敗")
                return {
                    'selected_data': [],
                    'outlier_data': [],
                    'total_indicators': 0,
                    'valid_indicators': 0,
                    'selection_reason': 'fallback_failed'
                }

    def _validate_corrected_value(self, corrected_value, indicator_name):
        """
        修正値の妥当性をチェック

        Args:
            corrected_value: 修正された値
            indicator_name: 指標名

        Returns:
            bool: 修正値が妥当かどうか
        """
        try:
            if corrected_value is None:
                return False

            # 指標別の厳格な妥当性チェック
            if "失業率" in indicator_name or "完全失業率" in indicator_name:
                return 1.0 <= corrected_value <= 5.0  # 失業率: 1-5%

            elif "有効求人倍率" in indicator_name:
                return 0.5 <= corrected_value <= 2.0  # 有効求人倍率: 0.5-2.0倍

            elif (("鉱工業" in indicator_name or "生産指数" in indicator_name or
                   "出荷指数" in indicator_name) and "指数" in indicator_name):
                return 50 <= corrected_value <= 200  # 生産指数系: 50-200

            elif "第三次産業活動指数" in indicator_name:
                return 80 <= corrected_value <= 150  # 第三次産業活動指数: 80-150

            elif "物価指数" in indicator_name:
                return 50 <= corrected_value <= 200  # 物価指数: 50-200

            else:
                # 一般的な範囲チェック
                return -1000000 <= corrected_value <= 1000000

        except Exception as e:
            logger.error("修正値妥当性チェックエラー: %s", e)
            return False

    def _get_additional_indicators(self, current_count):
        """
        代替指標を取得（異常値除外後の補完用）

        Args:
            current_count: 現在の有効指標数

        Returns:
            追加の有効指標リスト
        """
        try:
            if current_count >= 3:  # 十分な数がある場合は追加しない
                return []

            logger.info("代替指標取得を開始（現在: %d件）", current_count)

            # 指標設定を再読み込み
            indicators = load_indicators_config()
            if not indicators:
                logger.warning("指標設定の再読み込みに失敗")
                return []

            # 重要度の高い指標を優先選択
            high_priority_indicators = [
                indicator for indicator in indicators
                if indicator.get('importance') == 'high'
            ]

            # ランダムシャッフル
            random.shuffle(high_priority_indicators)

            additional_data = []
            max_attempts = min(10, len(high_priority_indicators))  # 最大10件まで試行

            for i, indicator in enumerate(high_priority_indicators[:max_attempts]):
                if len(additional_data) >= (3 - current_count):  # 必要数に達したら終了
                    break

                try:
                    logger.info("代替指標取得試行 (%d/%d): %s", i+1, max_attempts, indicator['name'])
                    data = self.data_collector.get_indicator_data(indicator)

                    if data:
                        # 品質チェック
                        original_value = data.get('value')
                        if original_value is not None and original_value != 'N/A':
                            # 簡易異常値チェック
                            is_valid = self._validate_corrected_value(original_value, data['name'])
                            if is_valid:
                                additional_data.append(data)
                                logger.info("代替指標取得成功: %s = %s", data['name'], original_value)
                            else:
                                logger.warning("代替指標も異常値: %s = %s", data['name'], original_value)
                        else:
                            logger.warning("代替指標の値が無効: %s", indicator['name'])
                    else:
                        logger.warning("代替指標取得失敗: %s", indicator['name'])

                except Exception as e:
                    logger.error("代替指標取得エラー (%s): %s", indicator['name'], e)
                    continue

            logger.info("代替指標取得完了: %d件", len(additional_data))
            return additional_data

        except Exception as e:
            logger.error("代替指標選択エラー: %s", e)
            return []

    def _clean_historical_data(self, historical_data, indicator_name):
        """
        時系列データから異常値を除外（トレンド分析の品質向上）

        Args:
            historical_data: 時系列データのリスト
            indicator_name: 指標名

        Returns:
            クリーニングされた時系列データ
        """
        try:
            if not historical_data:
                return []

            clean_data = []
            for item in historical_data:
                value = item.get('$')
                if value is None:
                    continue

                try:
                    numeric_value = float(value)

                    # 指標別の異常値チェック
                    is_valid = True

                    if "失業率" in indicator_name:
                        if numeric_value > 6.0 or numeric_value < 1.0:
                            is_valid = False
                    elif "有効求人倍率" in indicator_name:
                        if numeric_value > 3.0 or numeric_value < 0.3:
                            is_valid = False
                    elif "機械受注" in indicator_name:
                        if numeric_value > 20000 or (numeric_value < 5000 and numeric_value > 100):
                            is_valid = False
                    elif "輸出" in indicator_name or "輸入" in indicator_name:
                        if numeric_value > 100000 or (numeric_value < 30000 and numeric_value > 1000):
                            is_valid = False
                    elif "物価指数" in indicator_name:
                        if numeric_value > 200 or numeric_value < 50:
                            is_valid = False
                    elif "指数" in indicator_name:
                        if numeric_value > 1000 or numeric_value < 10:
                            is_valid = False

                    if is_valid:
                        clean_data.append(item)
                    else:
                        logger.debug("時系列データから異常値除外: %s = %s", indicator_name, numeric_value)

                except (ValueError, TypeError):
                    logger.debug("時系列データの数値変換失敗: %s", value)
                    continue

            return clean_data

        except Exception as e:
            logger.error("時系列データクリーニングエラー: %s", e)
            return historical_data  # エラー時は元データを返す

    def analyze_data(self, data_list):
        """データを分析（多様な指標選択と時事ニュース統合）"""
        try:
            if not data_list:
                logger.warning("分析対象のデータがありません")
                return None

            # NumPy型を標準Python型に変換
            self.convert_numpy_types_in_data_list(data_list)

            # 異常値フィルタリングと多様な指標選択
            selection_result = self.filter_outliers_and_select_indicators(data_list)

            if not selection_result:
                logger.error("指標選択に失敗しました。有効なデータがありません。")
                return None

            selected_data = selection_result.get('selected_data', [])
            outlier_data = selection_result.get('outlier_data', [])
            selection_reason = selection_result.get('selection_reason', 'unknown')

            if not selected_data:
                logger.error("選択された指標がありません。全て異常値として除外されました。")
                return None
            
            logger.info("経済データ分析を開始します（%d件の指標を分析、選択理由: %s）", len(selected_data), selection_reason)

            # 関連ニュースを取得（強化版）
            try:
                relevant_news = self.news_collector.get_relevant_news(selected_data, max_news=5)
                news_context = self.news_collector.format_news_for_analysis(relevant_news)
                logger.info("関連ニュース取得完了: %d件", len(relevant_news))

                # ニュースが取得できない場合は経済ニュースを一般的に取得
                if not relevant_news:
                    logger.info("指標関連ニュースが見つからないため、一般経済ニュースを取得")
                    general_news = self.news_collector.get_general_economic_news(max_news=3)
                    if general_news:
                        relevant_news = general_news
                        news_context = self.news_collector.format_news_for_analysis(general_news)
                        logger.info("一般経済ニュース取得完了: %d件", len(general_news))

            except Exception as e:
                logger.warning("ニュース取得に失敗: %s", e)
                relevant_news = []
                news_context = ""

            # 関連ニュースを保存（動的ハッシュタグ生成用）
            self.current_relevant_news = relevant_news
            # 選択データを保存（グラフ作成用）
            self.current_selected_data = selected_data
            
            # 時系列分析情報を整理
            time_series_info = []
            for data in selected_data:
                ts_analysis = data.get('time_series_analysis', {})
                if ts_analysis:
                    time_series_info.append(
                        "- %s: %s" % (data['name'], ts_analysis.get('trend', '不明'))
                    )
            
            time_series_context = "\n【時系列トレンド分析】\n" + "\n".join(time_series_info) if time_series_info else ""
            
            # 異常値情報を整理
            outlier_context = ""
            if outlier_data:
                outlier_info = []
                for data in outlier_data:
                    outlier_analysis = data.get('outlier_analysis', {})
                    if outlier_analysis.get('has_outliers'):
                        reasons = outlier_analysis.get('outlier_info', {}).get('reasons', [])
                        outlier_info.append("- %s: %s" % (data['name'], ', '.join(reasons)))
                
                if outlier_info:
                    outlier_context = "\n【除外された異常値指標（非現実的数値）】\n" + "\n".join(outlier_info)
                    outlier_context += "\n※これらの異常値は分析対象から除外し、現実的な数値のみを使用"
            
            # 統合コンテキストを作成
            analysis_context = "%s%s\n\n%s" % (time_series_context, outlier_context, news_context)
            
            # Gemini AIで分析実行
            analysis_result = self.analyzer.analyze_economic_data_with_context(selected_data, analysis_context)
            
            if analysis_result:
                logger.info("データ分析が完了しました")
                return analysis_result
            else:
                logger.error("データ分析に失敗しました")
                return None
                
        except Exception as e:
            logger.error("データ分析エラー: %s", e)
            return None
    
    def post_to_twitter(self, analysis_result):
        """分析結果をTwitterに投稿（画像付き）"""
        try:
            logger.info("Twitter投稿を開始します")

            # Twitter投稿用コンテンツを生成
            tweet_content = self.analyzer.generate_tweet_content(analysis_result)

            if not tweet_content:
                logger.error("Twitter投稿用コンテンツの生成に失敗しました")
                return None

            # 動的ハッシュタグを生成
            dynamic_hashtag = self.news_collector.generate_dynamic_hashtag(self.current_relevant_news)
            logger.info("生成された動的ハッシュタグ: %s", dynamic_hashtag)

            # ハッシュタグを追加
            tweet_with_hashtags = self.twitter_poster.add_hashtags(
                tweet_content,
                dynamic_hashtag=dynamic_hashtag
            )

            logger.info("投稿予定内容: %s", tweet_with_hashtags)
            logger.info("文字数: %s", len(tweet_with_hashtags))

            # グラフを作成
            chart_path = self.create_chart_for_tweet()

            # 画像付きTwitter投稿実行
            if chart_path:
                tweet_id = self.twitter_poster.post_tweet_with_optional_image(tweet_with_hashtags, chart_path)
            else:
                # 画像作成に失敗した場合はテキストのみ投稿
                tweet_id = self.twitter_poster.post_tweet(tweet_with_hashtags)

            if tweet_id:
                logger.info("Twitter投稿成功: ID %s", tweet_id)
                return tweet_id
            else:
                logger.error("Twitter投稿に失敗しました")
                return None

        except Exception as e:
            logger.error("Twitter投稿エラー: %s", e)
            return None

    def create_chart_for_tweet(self):
        """ツイート用のチャートを作成（複数指標対応）"""
        try:
            if not self.current_selected_data:
                logger.warning("チャート作成用のデータがありません")
                return None

            logger.info("統計データのチャートを作成中... (指標数: %d)", len(self.current_selected_data))

            # 複数指標に対応したチャート作成
            if len(self.current_selected_data) == 1:
                # 単一指標の場合：詳細な時系列チャート
                selected_indicator = self.current_selected_data[0]
                chart_title = self._generate_chart_title([selected_indicator])
                chart_path = self._create_flexible_single_indicator_chart(
                    selected_indicator,
                    chart_title
                )
            elif len(self.current_selected_data) <= 3:
                # 複数指標の場合：比較チャートまたは複合チャート
                chart_title = self._generate_chart_title(self.current_selected_data)
                chart_path = self._create_multi_indicator_chart(
                    self.current_selected_data,
                    chart_title
                )
            else:
                # 4つ以上の場合：最初の3つを使用
                selected_indicators = self.current_selected_data[:3]
                chart_title = self._generate_chart_title(selected_indicators)
                chart_path = self._create_multi_indicator_chart(
                    selected_indicators,
                    chart_title
                )

                logger.info("単一指標チャート作成: %s", selected_indicator['name'])
            else:
                logger.warning("チャート作成用のデータがありません")
                chart_path = None

            if chart_path:
                logger.info("チャート作成成功: %s", chart_path)
                # 古いチャートファイルをクリーンアップ
                self.chart_generator.cleanup_old_charts(max_age_hours=24)
            else:
                logger.warning("チャート作成に失敗しました")

            return chart_path

        except Exception as e:
            logger.error("チャート作成エラー: %s", e)
            return None

    def _create_multi_indicator_chart(self, indicators, title):
        """
        複数指標の比較チャート作成

        Args:
            indicators: 指標データのリスト（2-3個）
            title: チャートタイトル

        Returns:
            str: チャートファイルのパス（失敗時はNone）
        """
        try:
            logger.info("複数指標チャート作成開始: %d指標", len(indicators))

            # チャートタイプを決定
            chart_types = [
                'multi_bar_comparison',    # 複数指標の棒グラフ比較
                'multi_line_comparison',   # 複数指標の線グラフ比較
                'normalized_comparison',   # 正規化比較チャート
                'category_breakdown'       # カテゴリ別分解チャート
            ]

            # ランダムにチャートタイプを選択
            import random
            selected_chart_type = random.choice(chart_types)
            logger.info("選択されたチャートタイプ: %s", selected_chart_type)

            # 指標の特性に応じてチャートタイプを調整
            if selected_chart_type == 'multi_bar_comparison':
                return self._create_multi_bar_chart(indicators, title)
            elif selected_chart_type == 'multi_line_comparison':
                return self._create_multi_line_chart(indicators, title)
            elif selected_chart_type == 'normalized_comparison':
                return self._create_normalized_comparison_chart(indicators, title)
            else:  # category_breakdown
                return self._create_category_breakdown_chart(indicators, title)

        except Exception as e:
            logger.error("複数指標チャート作成エラー: %s", e)
            # フォールバック: 最初の指標で単一チャートを作成
            if indicators:
                logger.info("フォールバック: 単一指標チャートを作成")
                return self._create_flexible_single_indicator_chart(indicators[0], title)
            return None

    def _create_multi_bar_chart(self, indicators, title):
        """複数指標の棒グラフ比較チャート"""
        try:
            chart_data = []
            for indicator in indicators:
                chart_data.append({
                    'name': indicator['name'],
                    'value': indicator['value'],
                    'unit': indicator.get('unit', ''),
                    'category': indicator.get('category', 'その他')
                })

            return self.chart_generator.create_multi_indicator_bar_chart(
                chart_data, title, chart_type='comparison'
            )
        except Exception as e:
            logger.error("複数棒グラフ作成エラー: %s", e)
            return None

    def _create_multi_line_chart(self, indicators, title):
        """複数指標の線グラフ比較チャート"""
        try:
            # 時系列データがある指標のみを使用
            time_series_indicators = []
            for indicator in indicators:
                if indicator.get('time_series_data'):
                    time_series_indicators.append(indicator)

            if len(time_series_indicators) >= 2:
                return self.chart_generator.create_multi_line_chart(
                    time_series_indicators, title
                )
            else:
                # 時系列データが不十分な場合は棒グラフにフォールバック
                return self._create_multi_bar_chart(indicators, title)
        except Exception as e:
            logger.error("複数線グラフ作成エラー: %s", e)
            return None

    def _create_normalized_comparison_chart(self, indicators, title):
        """正規化比較チャート（異なる単位の指標を比較）"""
        try:
            return self.chart_generator.create_normalized_comparison_chart(
                indicators, title
            )
        except Exception as e:
            logger.error("正規化比較チャート作成エラー: %s", e)
            return self._create_multi_bar_chart(indicators, title)

    def _create_category_breakdown_chart(self, indicators, title):
        """カテゴリ別分解チャート"""
        try:
            # カテゴリごとにグループ化
            category_groups = {}
            for indicator in indicators:
                category = indicator.get('category', 'その他')
                if category not in category_groups:
                    category_groups[category] = []
                category_groups[category].append(indicator)

            if len(category_groups) >= 2:
                return self.chart_generator.create_category_breakdown_chart(
                    category_groups, title
                )
            else:
                # カテゴリが少ない場合は通常の比較チャート
                return self._create_multi_bar_chart(indicators, title)
        except Exception as e:
            logger.error("カテゴリ分解チャート作成エラー: %s", e)
            return self._create_multi_bar_chart(indicators, title)

    def _create_flexible_single_indicator_chart(self, indicator, title):
        """
        単一指標の柔軟なチャート作成（指標特性に応じた最適なチャートタイプ選択）

        Args:
            indicator: 指標データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            indicator_name = indicator.get('name', 'Unknown')
            historical_data = indicator.get('historical_data', [])

            # 指標特性に基づく最適チャートタイプを決定
            optimal_chart_type = self._determine_optimal_chart_type(indicator)
            logger.info("Processing...")

            # 最適チャートタイプを最初に試行
            chart_path = self._create_chart_by_type(indicator, title, optimal_chart_type)
            if chart_path:
                logger.info("Processing...")
                return chart_path

            # 新しい自動選択機能を直接使用（フォールバック処理を簡素化）
            logger.info("新しい自動選択機能でチャート作成")
            chart_path = self.chart_generator.create_chart_with_fallback(
                data=indicator,
                title=title,
                chart_type_info=None
            )

            if chart_path:
                logger.info("チャート作成成功: %s", chart_path)
                return chart_path
            else:
                logger.warning("チャート作成失敗: %s", indicator_name)
                return None

        except Exception as e:
            logger.error("チャート作成エラー: %s", e)
            return None

    def _determine_optimal_chart_type(self, indicator):
        """
        指標の特性に基づいて最適なチャートタイプを決定（根本的見直し版）

        Args:
            indicator: 指標データ

        Returns:
            最適なチャートタイプ名
        """
        try:
            import random
            name = indicator.get('name', '')
            value = indicator.get('value')
            historical_data = indicator.get('historical_data', [])

            # 実際に有効な時系列データをカウント
            valid_time_series_count = self._count_valid_historical_data(historical_data)

            logger.info("=== チャートタイプ決定（新ロジック） ===")
            logger.info("指標名: %s", name)
            logger.info("履歴データ総数: %d件", len(historical_data))
            logger.info("有効な時系列データ: %d件", valid_time_series_count)

            # 有効な時系列データが3点以上ある場合は時系列に最適なチャートを選択
            if valid_time_series_count >= 3:
                # データの性質に応じて最適なチャートタイプを選択
                if valid_time_series_count >= 12:  # 1年以上のデータ
                    chart_types = ['time_series', 'area']
                    weights = [60, 40]  # 長期データには線グラフとエリアチャートが最適
                elif valid_time_series_count >= 6:  # 半年以上のデータ
                    chart_types = ['time_series', 'area', 'line_with_markers']
                    weights = [45, 35, 20]  # 中期データには多様性を持たせる
                else:  # 3-5点のデータ
                    chart_types = ['line_with_markers', 'time_series']
                    weights = [60, 40]  # 少ないデータにはマーカー付きが見やすい

                selected = random.choices(chart_types, weights=weights, k=1)[0]
                logger.info("✅ 時系列最適化チャート選択: %s（有効データ%d件、データ量考慮選択）", selected, valid_time_series_count)
                return selected

            # 有効データが2点の場合は多様なチャートから選択
            elif valid_time_series_count == 2:
                chart_types = ['enhanced_bar', 'pie', 'donut', 'radar']
                weights = [40, 25, 20, 15]  # enhanced_barを優先しつつ多様性確保
                selected = random.choices(chart_types, weights=weights, k=1)[0]
                logger.info("✅ 2点データチャート選択: %s（有効データ2件、重み付き選択）", selected)
                return selected

            # 有効データが1点以下の場合は単一値チャート
            else:
                # 指標の性質に応じて多様なチャートタイプを選択
                if any(keyword in name for keyword in ['率', '%', 'パーセント']):
                    chart_types = ['pie', 'donut', 'enhanced_bar']
                    selected = random.choice(chart_types)
                    logger.info("✅ 率系チャート選択: %s", selected)
                    return selected

                elif any(keyword in name for keyword in ['指数', 'インデックス']):
                    chart_types = ['enhanced_bar', 'radar', 'pie']
                    selected = random.choice(chart_types)
                    logger.info("✅ 指数系チャート選択: %s", selected)
                    return selected

                elif any(keyword in name for keyword in ['額', '円', '金額']):
                    chart_types = ['enhanced_bar', 'pie', 'donut']
                    selected = random.choice(chart_types)
                    logger.info("✅ 金額系チャート選択: %s", selected)
                    return selected

                else:
                    chart_types = ['enhanced_bar', 'pie', 'donut', 'radar']
                    selected = random.choice(chart_types)
                    logger.info("✅ 一般チャート選択: %s", selected)
                    return selected

        except Exception as e:
            logger.error("チャートタイプ決定エラー: %s", e)
            # エラー時はシンプルな棒グラフ
            return 'enhanced_bar'

    def _count_valid_historical_data(self, historical_data):
        """
        有効な時系列データの数をカウント

        Args:
            historical_data: 履歴データリスト

        Returns:
            有効なデータポイント数
        """
        valid_count = 0
        for data_point in historical_data:
            if isinstance(data_point, dict):
                value = data_point.get('$')
                time_field = data_point.get('@time')

                # 値が有効で、時間情報があるかチェック
                if (value is not None and
                    str(value) != 'nan' and
                    str(time_field) != 'NaT' and
                    time_field is not None):
                    try:
                        float(value)
                        valid_count += 1
                    except (ValueError, TypeError):
                        continue

        return valid_count

    def _create_chart_by_type(self, indicator, title, chart_type):
        """
        指定されたタイプのチャートを作成

        Args:
            indicator: 指標データ
            title: チャートタイトル
            chart_type: チャートタイプ

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # 新しいチャート生成システムを使用（多様性確保）
            logger.info("チャートタイプ '%s' でチャート作成開始", chart_type)

            # chart_generatorの新しいcreate_chart_with_fallbackメソッドを使用
            chart_type_info = {'type': chart_type, 'reason': 'main.py指定'}
            chart_path = self.chart_generator.create_chart_with_fallback(
                data=indicator,
                title=title,
                chart_type_info=chart_type_info
            )

            if chart_path:
                logger.info("チャート作成成功: %s (タイプ: %s)", chart_path, chart_type)
                return chart_path
            else:
                logger.warning("チャート作成失敗: %s", chart_type)
                # 緊急フォールバック
                return self.chart_generator.create_enhanced_bar_chart(indicator, title)

        except Exception as e:
            logger.error("チャート作成エラー: %s", e)
            # 緊急フォールバック
            try:
                return self.chart_generator.create_enhanced_bar_chart(indicator, title)
            except:
                return None

    def _generate_chart_title(self, selected_data):
        """選択されたデータに基づいて動的にチャートタイトルを生成（複数指標対応）"""
        try:
            if not selected_data:
                return "経済指標分析"

            # 年度を抽出（最新のデータから、NaTやN/Aを除外）
            year = None
            for data in selected_data:
                data_year = data.get('year')
                # NaTやN/A、空文字列を除外
                if data_year and str(data_year).upper() not in ['N/A', 'NAT', 'NULL', ''] and 'nat' not in str(data_year).lower():
                    try:
                        # 数値として有効な年度かチェック
                        year_int = int(float(str(data_year)))
                        if 2000 <= year_int <= 2030:  # 妥当な年度範囲
                            year = str(year_int)
                            break
                    except (ValueError, TypeError):
                        continue

            # 複数指標の場合のタイトル生成
            if len(selected_data) > 1:
                # カテゴリベースのタイトル
                categories = list(set(data.get('category', 'その他') for data in selected_data))
                if len(categories) == 1:
                    # 同一カテゴリの場合
                    category_title = categories[0]
                    if year:
                        return f"{year}年 {category_title}指標比較"
                    else:
                        return f"{category_title}指標比較"
                else:
                    # 異なるカテゴリの場合
                    if len(selected_data) == 2:
                        if year:
                            return f"{year}年 経済指標比較分析"
                        else:
                            return "経済指標比較分析"
                    else:
                        if year:
                            return f"{year}年 多角的経済分析"
                        else:
                            return "多角的経済分析"

            # カテゴリを抽出
            categories = set()
            for data in selected_data:
                category = data.get('category', '')
                if category:
                    categories.add(category)

            # 主要な指標名を抽出（最大2つ）
            main_indicators = []
            for data in selected_data[:2]:
                name = data.get('name', '')
                if name:
                    # 長い名前は短縮
                    if len(name) > 8:
                        if '指数' in name:
                            name = name.replace('指数', '').strip()
                        if '（' in name:
                            name = name.split('（')[0].strip()
                    main_indicators.append(name)

            # 年度付きタイトル生成（「等の分析」を削除、NaTを除外）
            year_prefix = "%s年 " % year if year else ""

            if len(categories) == 1:
                category = list(categories)[0]
                if len(main_indicators) == 1:
                    return "%s%s" % (year_prefix, main_indicators[0])
                elif len(main_indicators) == 2:
                    return "%s%s・%s" % (year_prefix, main_indicators[0], main_indicators[1])
                else:
                    return "%s%s指標" % (year_prefix, category)
            elif len(main_indicators) >= 2:
                return "%s%s・%s" % (year_prefix, main_indicators[0], main_indicators[1])
            elif len(main_indicators) == 1:
                return "%s%s" % (year_prefix, main_indicators[0])
            else:
                return "%s経済指標" % year_prefix

        except Exception as e:
            logger.error("チャートタイトル生成エラー: %s", e)
            return "経済指標分析"
    
    def get_additional_indicators(self, existing_data):
        """代替指標を取得"""
        try:
            logger.info("代替指標取得を開始（現在: %d件）", len(existing_data))

            # 既存の指標名を取得
            existing_names = {data.get('name', '') for data in existing_data}

            # 代替指標リスト（確実に取得できるもの）
            fallback_indicators = [
                "消費者物価指数（総合）",
                "鉱工業生産指数",
                "第三次産業活動指数",
                "完全失業率",
                "有効求人倍率",
                "新設住宅着工戸数",
                "機械受注（船舶・電力を除く民需）",
                "輸出額",
                "輸入額",
                "日経平均株価"
            ]

            additional_data = []
            for indicator_name in fallback_indicators:
                if indicator_name not in existing_names and len(additional_data) < 5:
                    logger.info("代替指標取得試行 (%d/%d): %s", len(additional_data)+1, 10, indicator_name)

                    # 指標設定を取得
                    indicator_config = None
                    for config in self.data_collector.indicators:
                        if config['name'] == indicator_name:
                            indicator_config = config
                            break

                    if indicator_config and not indicator_config.get('disabled', False):
                        try:
                            data = self.data_collector.get_data(indicator_config)
                            if data and data.get('value') is not None:
                                # 簡単な異常値チェック
                                value = data.get('value')
                                if isinstance(value, (int, float)) and not (value <= 0 or value > 1000000):
                                    additional_data.append(data)
                                    logger.info("代替指標取得成功: %s = %s", indicator_name, value)
                                else:
                                    logger.warning("代替指標異常値: %s = %s", indicator_name, value)
                            else:
                                logger.warning("代替指標データなし: %s", indicator_name)
                        except Exception as e:
                            logger.warning("代替指標取得エラー: %s - %s", indicator_name, e)
                    else:
                        logger.warning("代替指標設定なし: %s", indicator_name)

            logger.info("代替指標取得完了: %d件", len(additional_data))
            return additional_data

        except Exception as e:
            logger.error("代替指標取得でエラー: %s", e)
            return []

    def get_emergency_fallback_indicators(self):
        """緊急フォールバック: より多くの指標を緩い条件で取得"""
        try:
            logger.info("緊急フォールバック指標取得を開始")

            # より広範囲の指標リスト
            emergency_indicators = [
                "GDP（国内総生産）",
                "人口推計",
                "婚姻件数",
                "離婚件数",
                "死亡数",
                "小売業販売額",
                "企業短期経済観測調査（大企業製造業）",
                "景気動向指数（一致指数）",
                "自動車販売台数",
                "景気動向指数（遅行指数）",
                "景気動向指数（先行指数）",
                "教育費"
            ]

            emergency_data = []
            for indicator_name in emergency_indicators:
                if len(emergency_data) >= 3:  # 最大3件まで
                    break

                logger.info("緊急指標取得試行: %s", indicator_name)

                # 指標設定を取得
                indicator_config = None
                for config in self.data_collector.indicators:
                    if config['name'] == indicator_name:
                        indicator_config = config
                        break

                if indicator_config and not indicator_config.get('disabled', False):
                    try:
                        data = self.data_collector.get_data(indicator_config)
                        if data and data.get('value') is not None:
                            value = data.get('value')

                            # 緩い異常値チェック（より多くの値を受け入れる）
                            if isinstance(value, (int, float)) and value > 0:
                                # 極端に大きい値のみ除外
                                if value < 1e10:  # 100億未満なら受け入れ
                                    emergency_data.append(data)
                                    logger.info("緊急指標取得成功: %s = %s", indicator_name, value)
                                else:
                                    logger.warning("緊急指標でも極端値: %s = %s", indicator_name, value)
                            else:
                                logger.warning("緊急指標無効値: %s = %s", indicator_name, value)
                        else:
                            logger.warning("緊急指標データなし: %s", indicator_name)
                    except Exception as e:
                        logger.warning("緊急指標取得エラー: %s - %s", indicator_name, e)
                else:
                    logger.warning("緊急指標設定なし: %s", indicator_name)

            logger.info("緊急フォールバック完了: %d件", len(emergency_data))
            return emergency_data

        except Exception as e:
            logger.error("緊急フォールバック取得でエラー: %s", e)
            return []

    def save_results(self, data_list, analysis_result, tweet_id=None):
        """結果をファイルに保存"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            results = {
                "timestamp": timestamp,
                "data_count": len(data_list),
                "collected_data": data_list,
                "analysis_result": analysis_result,
                "tweet_id": tweet_id,
                "tweet_url": "https://twitter.com/i/web/status/%s" % tweet_id if tweet_id else None
            }

            # 結果ディレクトリを作成
            os.makedirs("results", exist_ok=True)

            # JSONファイルに保存
            filename = "results/economic_analysis_%s.json" % timestamp
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)

            logger.info("結果を保存しました: %s", filename)

        except Exception as e:
            logger.error("結果保存エラー: %s", e)
    
    def run(self):
        """メイン実行フロー"""
        try:
            logger.info("=== 経済統計分析・投稿ボット開始 ===")
            
            # 1. データ収集
            data_list = self.collect_economic_data()

            if not data_list:
                logger.warning("データ収集に失敗しました。GitHub Actionでは一時的な問題の可能性があります。")
                logger.info("処理を正常終了します（exit code 0）。")
                return True  # GitHub Actionでエラーにならないよう成功として扱う
            
            # 2. データ分析
            analysis_result = self.analyze_data(data_list)

            if not analysis_result:
                logger.warning("データ分析に失敗しました。GitHub Actionでは一時的な問題の可能性があります。")
                logger.info("処理を正常終了します（exit code 0）。")
                return True  # GitHub Actionでエラーにならないよう成功として扱う
            
            # 3. Twitter投稿（レート制限で失敗しても継続）
            tweet_id = self.post_to_twitter(analysis_result)
            
            # 4. 結果保存（Twitter投稿の成否に関わらず保存）
            self.save_results(data_list, analysis_result, tweet_id)
            
            if tweet_id:
                logger.info("=== 全処理が正常に完了しました ===")
                logger.info("投稿URL: https://twitter.com/i/web/status/%s", tweet_id)
                return True
            else:
                logger.warning("=== データ分析は完了しましたが、Twitter投稿に失敗しました ===")
                logger.warning("分析結果は保存されました。後でTwitter投稿を再試行できます。")
                return True  # 分析が完了していれば成功とみなす
                
        except Exception as e:
            logger.error("メイン実行エラー: %s", e)
            return False

def main():
    """エントリーポイント"""
    try:
        # ボット初期化
        bot = EconomicStatsBot()
        
        # 実行
        success = bot.run()
        
        # 終了コード設定
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error("アプリケーション実行エラー: %s", e)
        sys.exit(1)

if __name__ == "__main__":
    main()

