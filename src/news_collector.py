"""
時事ニュース取得モジュール
"""

import os
import requests
import json
import feedparser
import re
import random
from typing import List, Dict, Optional
import logging
from datetime import datetime, timedelta
from bs4 import BeautifulSoup

# ログ設定
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NewsCollector:
    """経済関連ニュースを収集するクラス"""
    
    def __init__(self):
        """初期化"""
        self.rss_feeds = [
            {
                "name": "NHKニュース経済",
                "url": "https://www3.nhk.or.jp/rss/news/cat6.xml",
                "category": "経済"
            },
            {
                "name": "Yahoo!ニュース経済",
                "url": "https://news.yahoo.co.jp/rss/categories/business.xml",
                "category": "経済"
            },
            {
                "name": "朝日新聞経済",
                "url": "https://www.asahi.com/rss/asahi/business.rdf",
                "category": "経済"
            }
        ]
        
        # 経済関連キーワード
        self.economic_keywords = [
            "GDP", "物価", "インフレ", "デフレ", "失業率", "金融政策", "日銀", "金利",
            "消費者物価指数", "CPI", "景気", "経済成長", "貿易", "輸出", "輸入",
            "機械受注", "設備投資", "消費", "雇用", "労働市場", "賃金", "円安", "円高",
            "株価", "日経平均", "為替", "製造業", "サービス業", "企業業績",
            "鉱工業生産", "小売業", "住宅着工", "有効求人倍率", "現金給与", "法人企業統計",
            "景気動向指数", "企業物価指数", "出荷指数", "在庫指数", "家計消費支出"
        ]

        # ハッシュタグ生成用キーワード（ニュースから抽出される可能性の高い単語）
        self.hashtag_keywords = [
            "金融政策", "インフレ", "デフレ", "円安", "円高", "株価", "日銀", "金利",
            "貿易", "輸出", "輸入", "GDP", "景気", "雇用", "失業", "賃金", "消費",
            "投資", "製造業", "サービス業", "建設", "不動産", "エネルギー", "原油",
            "為替", "ドル", "ユーロ", "中国", "アメリカ", "EU", "ASEAN", "G7",
            "コロナ", "ワクチン", "デジタル", "AI", "脱炭素", "SDGs", "ESG",
            "半導体", "自動車", "観光", "航空", "鉄道", "小売", "百貨店", "コンビニ",
            "銀行", "証券", "保険", "年金", "税制", "予算", "財政", "国債"
        ]

        # HTTPリクエスト用のヘッダー
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    def get_mock_news(self) -> List[Dict]:
        """
        モックニュースデータを生成（RSS取得が困難な場合の代替）
        
        Returns:
            ニュースデータのリスト
        """
        mock_news = [
            {
                "title": "日銀、金融政策の現状維持を決定　物価目標達成への道筋を慎重に検討",
                "summary": "日本銀行は金融政策決定会合で、大規模な金融緩和政策の維持を決定。2%の物価安定目標達成に向けた取り組みを継続する方針を確認した。",
                "category": "金融政策",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "relevance": "high"
            },
            {
                "title": "11月の消費者物価指数、前年同月比2.8%上昇　エネルギー価格が押し上げ",
                "summary": "総務省が発表した11月の全国消費者物価指数は、エネルギー価格の上昇により前年同月比2.8%の上昇となった。生鮮食品を除く総合指数も堅調な伸びを示している。",
                "category": "物価",
                "date": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
                "relevance": "high"
            },
            {
                "title": "10月の機械受注、前月比3.2%増　設備投資意欲の回復が鮮明に",
                "summary": "内閣府が発表した10月の機械受注統計では、設備投資の先行指標となる民需（船舶・電力を除く）が前月比3.2%増となり、企業の投資意欲回復が確認された。",
                "category": "設備投資",
                "date": (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d"),
                "relevance": "medium"
            },
            {
                "title": "労働市場の逼迫続く　10月の有効求人倍率1.29倍、完全失業率2.5%",
                "summary": "厚生労働省の発表によると、10月の有効求人倍率は1.29倍と高水準を維持。総務省発表の完全失業率も2.5%と低水準で、労働市場の逼迫状況が続いている。",
                "category": "雇用",
                "date": (datetime.now() - timedelta(days=3)).strftime("%Y-%m-%d"),
                "relevance": "high"
            },
            {
                "title": "円安進行で輸入物価が上昇　企業の価格転嫁の動向に注目",
                "summary": "為替市場で円安が進行する中、輸入物価の上昇が企業業績に影響を与えている。価格転嫁の進展度合いが今後の物価動向を左右する重要な要因となっている。",
                "category": "為替・貿易",
                "date": (datetime.now() - timedelta(days=4)).strftime("%Y-%m-%d"),
                "relevance": "medium"
            }
        ]
        
        return mock_news

    def fetch_rss_news(self, max_news_per_feed: int = 10) -> List[Dict]:
        """
        実際のRSSフィードからニュースを取得

        Args:
            max_news_per_feed: フィードあたりの最大ニュース数

        Returns:
            ニュースデータのリスト
        """
        all_news = []

        for feed_info in self.rss_feeds:
            try:
                logger.info(f"RSSフィード取得開始: {feed_info['name']}")

                # RSSフィードを取得
                response = requests.get(
                    feed_info['url'],
                    headers=self.headers,
                    timeout=10
                )
                response.raise_for_status()

                # feedparserでRSSを解析
                feed = feedparser.parse(response.content)

                if not feed.entries:
                    logger.warning(f"RSSフィードにエントリがありません: {feed_info['name']}")
                    continue

                # 各エントリを処理
                for entry in feed.entries[:max_news_per_feed]:
                    try:
                        # タイトルと概要を取得
                        title = entry.get('title', '').strip()
                        summary = entry.get('summary', entry.get('description', '')).strip()

                        # HTMLタグを除去
                        if summary:
                            summary = self._clean_html(summary)

                        # 日付を処理
                        published_date = entry.get('published_parsed')
                        if published_date:
                            date_str = datetime(*published_date[:6]).strftime("%Y-%m-%d")
                        else:
                            date_str = datetime.now().strftime("%Y-%m-%d")

                        # リンクを取得
                        link = entry.get('link', '')

                        if title and len(title) > 10:  # 最低限の長さをチェック
                            news_item = {
                                "title": title,
                                "summary": summary[:300] if summary else title,  # 概要は300文字まで
                                "category": feed_info['category'],
                                "date": date_str,
                                "source": feed_info['name'],
                                "link": link,
                                "relevance": "unknown"
                            }
                            all_news.append(news_item)

                    except Exception as e:
                        logger.warning(f"エントリ処理エラー ({feed_info['name']}): {e}")
                        continue

                logger.info(f"RSSフィード取得完了: {feed_info['name']} - {len([n for n in all_news if n['source'] == feed_info['name']])}件")

            except requests.RequestException as e:
                logger.error(f"RSSフィード取得エラー ({feed_info['name']}): {e}")
                continue
            except Exception as e:
                logger.error(f"RSSフィード処理エラー ({feed_info['name']}): {e}")
                continue

        logger.info(f"全RSSフィード取得完了: 合計{len(all_news)}件のニュース")
        return all_news

    def _clean_html(self, text: str) -> str:
        """
        HTMLタグを除去してテキストをクリーンアップ

        Args:
            text: HTMLを含む可能性のあるテキスト

        Returns:
            クリーンアップされたテキスト
        """
        try:
            # BeautifulSoupでHTMLタグを除去
            soup = BeautifulSoup(text, 'html.parser')
            clean_text = soup.get_text()

            # 余分な空白を除去
            clean_text = re.sub(r'\s+', ' ', clean_text).strip()

            return clean_text

        except Exception as e:
            logger.warning(f"HTMLクリーンアップエラー: {e}")
            # フォールバック：正規表現でHTMLタグを除去
            clean_text = re.sub(r'<[^>]+>', '', text)
            clean_text = re.sub(r'\s+', ' ', clean_text).strip()
            return clean_text

    def generate_dynamic_hashtag(self, news_list: List[Dict]) -> str:
        """
        関連ニュースから動的にハッシュタグを生成

        Args:
            news_list: 関連ニュースのリスト

        Returns:
            生成されたハッシュタグ（#付き）
        """
        try:
            if not news_list:
                return "#経済分析"  # デフォルト

            # 全ニュースのタイトルと概要を結合
            all_text = ""
            for news in news_list:
                title = news.get('title', '')
                summary = news.get('summary', '')
                all_text += f" {title} {summary}"

            # ハッシュタグ候補を抽出
            hashtag_candidates = []
            for keyword in self.hashtag_keywords:
                if keyword in all_text:
                    hashtag_candidates.append(keyword)

            # 候補がない場合は経済キーワードから検索
            if not hashtag_candidates:
                for keyword in self.economic_keywords:
                    if keyword in all_text:
                        hashtag_candidates.append(keyword)

            # 候補がある場合は最も頻出するものを選択
            if hashtag_candidates:
                # 頻度をカウント
                keyword_count = {}
                for keyword in hashtag_candidates:
                    count = all_text.count(keyword)
                    keyword_count[keyword] = count

                # 最も頻出するキーワードを選択
                most_frequent = max(keyword_count, key=keyword_count.get)

                # ハッシュタグ形式に変換（スペースを除去）
                hashtag = f"#{most_frequent.replace(' ', '').replace('　', '')}"
                logger.info(f"動的ハッシュタグ生成: {hashtag} (出現回数: {keyword_count[most_frequent]})")
                return hashtag

            # 候補がない場合はデフォルト
            logger.info("動的ハッシュタグ候補が見つからないため、デフォルトを使用")
            return "#経済分析"

        except Exception as e:
            logger.error(f"動的ハッシュタグ生成エラー: {e}")
            return "#経済分析"
    
    def analyze_news_relevance(self, news_list: List[Dict], economic_data: List[Dict]) -> List[Dict]:
        """
        ニュースと経済指標の関連性を分析
        
        Args:
            news_list: ニュースデータのリスト
            economic_data: 経済指標データのリスト
            
        Returns:
            関連性分析結果を含むニュースリスト
        """
        try:
            # 経済指標のカテゴリを抽出
            data_categories = set()
            for data in economic_data:
                category = data.get("category", "").lower()
                name = data.get("name", "").lower()
                data_categories.add(category)
                data_categories.add(name)
            
            analyzed_news = []
            for news in news_list:
                title = news.get("title", "").lower()
                summary = news.get("summary", "").lower()
                
                # 関連性スコア計算
                relevance_score = 0
                matched_keywords = []
                
                # カテゴリマッチング
                for category in data_categories:
                    if category in title or category in summary:
                        relevance_score += 2
                        matched_keywords.append(category)
                
                # キーワードマッチング
                for keyword in self.economic_keywords:
                    if keyword.lower() in title or keyword.lower() in summary:
                        relevance_score += 1
                        matched_keywords.append(keyword)
                
                # 関連性レベル決定
                if relevance_score >= 5:
                    relevance_level = "high"
                elif relevance_score >= 3:
                    relevance_level = "medium"
                elif relevance_score >= 1:
                    relevance_level = "low"
                else:
                    relevance_level = "none"
                
                news_analyzed = news.copy()
                news_analyzed.update({
                    "relevance_score": relevance_score,
                    "relevance_level": relevance_level,
                    "matched_keywords": list(set(matched_keywords))
                })
                
                analyzed_news.append(news_analyzed)
            
            # 関連性スコア順にソート
            analyzed_news.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            return analyzed_news
            
        except Exception as e:
            logger.error(f"ニュース関連性分析エラー: {e}")
            return news_list
    
    def get_relevant_news(self, economic_data: List[Dict], max_news: int = 5) -> List[Dict]:
        """
        経済指標に関連するニュースを取得

        Args:
            economic_data: 経済指標データのリスト
            max_news: 最大ニュース数

        Returns:
            関連ニュースのリスト
        """
        try:
            logger.info("経済関連ニュースの取得を開始")

            # 実際のRSSフィードからニュースを取得
            try:
                news_list = self.fetch_rss_news(max_news_per_feed=5)
                logger.info(f"RSSフィードから{len(news_list)}件のニュースを取得")
            except Exception as e:
                logger.warning(f"RSSフィード取得に失敗、モックデータを使用: {e}")
                news_list = self.get_mock_news()

            # ニュースが取得できない場合はモックデータを使用
            if not news_list:
                logger.warning("RSSフィードからニュースを取得できませんでした。モックデータを使用します。")
                news_list = self.get_mock_news()

            # 関連性分析
            analyzed_news = self.analyze_news_relevance(news_list, economic_data)

            # 時間単位でニュース選択に多様性を追加（より頻繁な変化）
            current_time = datetime.now()
            # 時間単位でシードを変更（日中でも異なるニュースを選択）
            hourly_seed = int(current_time.strftime("%Y%m%d%H")) + len(analyzed_news)
            random.seed(hourly_seed)
            logger.info("ニュース選択ランダム化 (時間単位シード: %d)", hourly_seed)

            # 関連性の高いニュースを選択（日替わりでランダム要素追加）
            high_relevance = [news for news in analyzed_news if news["relevance_level"] == "high"]
            medium_relevance = [news for news in analyzed_news if news["relevance_level"] == "medium"]
            low_relevance = [news for news in analyzed_news if news["relevance_level"] == "low"]

            # 各レベル内でランダムシャッフル（日替わり）
            random.shuffle(high_relevance)
            random.shuffle(medium_relevance)
            random.shuffle(low_relevance)

            # 優先度を考慮しつつ多様性を確保（強化版）
            relevant_news = []

            # より多様な選択戦略
            total_available = len(high_relevance) + len(medium_relevance) + len(low_relevance)

            if total_available > max_news:
                # 十分なニュースがある場合：バランス良く選択
                high_count = min(2, len(high_relevance), max_news // 2)  # 最大2件
                medium_count = min(2, len(medium_relevance), (max_news - high_count) // 2)  # 最大2件
                low_count = max_news - high_count - medium_count  # 残り

                relevant_news.extend(high_relevance[:high_count])
                relevant_news.extend(medium_relevance[:medium_count])
                if low_count > 0 and low_relevance:
                    relevant_news.extend(low_relevance[:low_count])

                logger.info("バランス選択: 高関連%d件 + 中関連%d件 + 低関連%d件",
                           high_count, medium_count, min(low_count, len(low_relevance)))
            else:
                # ニュースが少ない場合：利用可能なものを全て使用
                relevant_news.extend(high_relevance)
                remaining_slots = max_news - len(relevant_news)
                if remaining_slots > 0:
                    relevant_news.extend(medium_relevance[:remaining_slots])

                remaining_slots = max_news - len(relevant_news)
                if remaining_slots > 0:
                    relevant_news.extend(low_relevance[:remaining_slots])

                logger.info("全利用選択: 利用可能%d件から%d件を選択", total_available, len(relevant_news))

            # 最終的にシャッフルして順序をランダム化
            random.shuffle(relevant_news)

            # シード値をリセット
            random.seed()

            logger.info(f"関連ニュース取得完了: {len(relevant_news)}件")

            return relevant_news

        except Exception as e:
            logger.error(f"ニュース取得エラー: {e}")
            # エラー時はモックデータを返す
            try:
                mock_news = self.get_mock_news()
                analyzed_mock = self.analyze_news_relevance(mock_news, economic_data)
                return [news for news in analyzed_mock if news["relevance_level"] in ["high", "medium"]][:max_news]
            except:
                return []
    
    def format_news_for_analysis(self, news_list: List[Dict]) -> str:
        """
        ニュースを分析用フォーマットに変換
        
        Args:
            news_list: ニュースデータのリスト
            
        Returns:
            分析用フォーマットされた文字列
        """
        if not news_list:
            return "関連する時事ニュースは見つかりませんでした。"
        
        formatted_news = ["【関連する時事ニュース】"]
        
        for i, news in enumerate(news_list, 1):
            formatted_news.append(
                f"{i}. {news['title']}\n"
                f"   概要: {news['summary']}\n"
                f"   カテゴリ: {news['category']}\n"
                f"   関連度: {news.get('relevance_level', 'medium')}"
            )
        
        return "\n\n".join(formatted_news)

def main():
    """テスト用メイン関数"""
    collector = NewsCollector()
    
    # テスト用経済データ
    test_economic_data = [
        {"name": "消費者物価指数", "category": "物価"},
        {"name": "完全失業率", "category": "労働"},
        {"name": "機械受注", "category": "投資"}
    ]
    
    # ニュース取得テスト
    news = collector.get_relevant_news(test_economic_data)
    
    print("=== 取得されたニュース ===")
    for i, item in enumerate(news, 1):
        print(f"{i}. {item['title']}")
        print(f"   関連度: {item.get('relevance_level', 'unknown')}")
        print(f"   スコア: {item.get('relevance_score', 0)}")
        print()
    
    # フォーマット済みニュース
    print("=== フォーマット済みニュース ===")
    formatted = collector.format_news_for_analysis(news)
    print(formatted)

if __name__ == "__main__":
    main()