"""
Gemini AIを使用して経済データを分析するモジュール
"""

import os
import re
import logging

# Python 3.7対応: google.generativeaiが利用できない場合の対応
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("google.generativeai が利用できません。フォールバック分析を使用します。")

# ログ設定
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiAnalyzer:
    """Gemini AIを使用して経済データを分析するクラス"""
    
    def __init__(self, api_key):
        """
        初期化

        Args:
            api_key: Gemini APIキー
        """
        try:
            self.api_key = api_key

            if GEMINI_AVAILABLE:
                genai.configure(api_key=api_key)
                self.model = genai.GenerativeModel("gemini-2.5-flash-preview-05-20")
                logger.info("Gemini AI初期化完了")
            else:
                self.model = None
                logger.warning("Gemini AI利用不可。フォールバック分析を使用します。")

        except Exception as e:
            logger.error("Gemini AI初期化エラー: %s", e)
            self.model = None
        
    def analyze_economic_data(self, data_list):
        """
        経済データを分析してインサイトを生成

        Args:
            data_list: 経済指標データのリスト

        Returns:
            分析結果のテキスト、エラーの場合はNone
        """
        return self.analyze_economic_data_with_context(data_list, "")

    def analyze_economic_data_with_context(self, data_list, context=""):
        """
        経済データを分析してインサイトを生成（コンテキスト対応）

        Args:
            data_list: 経済指標データのリスト
            context: 追加のコンテキスト情報（異常検知結果など）

        Returns:
            分析結果のテキスト、エラーの場合はNone
        """
        try:
            # Gemini AIが利用できない場合はフォールバック分析を使用
            if not GEMINI_AVAILABLE or self.model is None:
                logger.info("Gemini AI利用不可。フォールバック分析を実行")
                return self._generate_fallback_analysis(data_list, context)

            # データを整理
            data_summary = self._prepare_data_summary(data_list)

            # 分析プロンプトを作成
            prompt = self._create_analysis_prompt(data_summary, context)

            # Gemini AIで分析実行
            logger.info("Gemini AIによる経済データ分析を開始")
            response = self.model.generate_content(prompt)
            
            # 安全性フィルターやその他の問題を詳細チェック
            if response.candidates:
                candidate = response.candidates[0]
                logger.info("レスポンス候補数: %s", len(response.candidates))
                logger.info("完了理由: %s", candidate.finish_reason)
                
                if candidate.finish_reason == 1:  # STOP
                    if candidate.content and candidate.content.parts:
                        result_text = candidate.content.parts[0].text
                        logger.info("分析完了")
                        return result_text.strip()
                    else:
                        logger.error("レスポンス候補にコンテンツが含まれていません")
                        return None
                elif candidate.finish_reason == 2:  # MAX_TOKENS
                    logger.warning("最大トークン数に達しました")
                    if candidate.content and candidate.content.parts:
                        result_text = candidate.content.parts[0].text
                        logger.info("分析完了（切り詰められました）")
                        return result_text.strip()
                    else:
                        logger.error("最大トークン数に達し、コンテンツが空です")
                        return None
                elif candidate.finish_reason == 3:  # SAFETY
                    logger.error("安全性フィルターによりコンテンツがブロックされました")
                    logger.error("安全性評価: %s", candidate.safety_ratings if hasattr(candidate, 'safety_ratings') else 'なし')
                    return None
                elif candidate.finish_reason == 4:  # RECITATION
                    logger.error("著作権保護によりコンテンツがブロックされました")
                    return None
                else:
                    logger.error("不明な完了理由: %s", candidate.finish_reason)
                    # フォールバック分析を試行
                    return self._generate_fallback_analysis(data_list, context)
            else:
                logger.error("Gemini AIからの応答候補が空です")
                # フォールバック分析を試行
                return self._generate_fallback_analysis(data_list, context)

        except Exception as e:
            logger.error("分析エラー: %s", e)
            # フォールバック分析を試行
            return self._generate_fallback_analysis(data_list, context)
    
    def _prepare_data_summary(self, data_list):
        """
        データを分析用に整理
        
        Args:
            data_list: 経済指標データのリスト
            
        Returns:
            整理されたデータサマリー
        """
        summary_parts = []
        
        for data in data_list:
            if data and "name" in data:
                # 頻度情報を日本語に変換
                frequency = data.get("frequency", "N/A")
                frequency_jp = {
                    "monthly": "月次",
                    "quarterly": "四半期",
                    "yearly": "年次",
                    "annual": "年次"
                }.get(frequency, frequency)

                # 年月情報を抽出
                year = data.get("year", "N/A")
                month = data.get("month", "N/A")
                time_info = data.get("time", "N/A")

                # 年月表示の生成
                if year != "N/A" and month != "N/A":
                    year_month = "%s年%d月" % (year, int(month))
                elif year != "N/A":
                    year_month = "%s年" % year
                else:
                    year_month = time_info

                summary_parts.append(
                    '- %s (%s): %s %s (%s、%s時点)' % (
                        data["name"],
                        data.get("description", "N/A"),
                        data.get("value", "N/A"),
                        data.get("unit", ""),
                        frequency_jp,
                        year_month
                    )
                )
        
        return "\n".join(summary_parts)
    
    def _create_analysis_prompt(self, data_summary, context=""):
        """
        分析用プロンプトを作成
        
        Args:
            data_summary: データサマリー
            context: 追加のコンテキスト情報
            
        Returns:
            分析プロンプト
        """
        context_section = "\n%s" % (context,) if context else ""
        
        prompt = """
あなたは著名な経済学者です。以下の日本の最新経済指標データ、時系列トレンド分析、および時事ニュースを総合的に分析し、経済学的理論と実践的知見を融合した高度な分析を行ってください。

【経済指標データ】
%s%s""" % (data_summary, context_section) + """

【経済学者としての分析要件】
1. マクロ経済学の理論（IS-LM分析、フィリップス曲線、成長理論等）を踏まえた解釈
2. 指標間の経済学的因果関係と相互作用メカニズムの分析
3. 強化された時系列データ分析（前年同月比、季節性、長期トレンド）
4. 時事ニュースと経済指標の具体的な関連性分析（重要）
5. 日本経済の構造的特徴（デフレ、少子高齢化、産業構造等）を考慮した洞察
6. 国際経済情勢（為替、貿易、金融政策等）との関連性
7. 政策含意（金融政策、財政政策）への示唆
8. 経済学者としての独自の視点と将来予測

【専門的分析の観点】
- 循環的要因 vs 構造的要因の識別
- 先行指標・一致指標・遅行指標としての性質と整合性
- 経済政策の有効性評価と今後の政策方向性
- リスク要因の特定と影響度評価
- 時事ニュースが示唆する経済環境の変化
- 多様な経済指標による総合的な経済情勢判断

【出力形式】
## 経済学者による日本経済総合分析

### 現状診断と時系列評価
[マクロ経済理論に基づく現状診断、時系列トレンドの経済学的解釈、データ品質評価]

### 時事ニュースとの統合分析（重要）
[経済指標と時事ニュースの具体的な関連性、ニュースが示唆する経済環境変化の指標への影響、政策・企業動向・国際情勢との関連性を詳細に分析]

### 構造的洞察と将来展望
[日本経済の構造的課題と特徴、経済指標が示す構造変化、中長期的な経済見通し]

### 政策含意と提言
[政策当局への具体的な示唆、推奨される政策対応、注意すべき政策リスク]

### Twitter投稿用サマリー
[130-140文字程度。「🔎Economistの視点：」で始まり、分析対象となった経済指標の具体的な数値と年月（例：「2024年11月の完全失業率2.4%」「2024年10月の機械受注3.2%増」など）を必ず明記。年月の表記は実際のデータ時点に基づいて正確に記載する。その数値の経済的意味や注目すべき変化を、経済専門家としての感想や驚き、関心を交えて表現し、時事ニュースとの関連も織り交ぜる。「〜に驚かされる」「〜を実感」「〜が興味深い」「〜に注目が集まる」「〜を強く感じる」などの感想表現を積極的に使用。単なるデータ報告ではなく、専門家の生の反応や洞察を伝える。技術的な問題、データ品質、異常値については一切言及しない。ハッシュタグは別途追加されるため本文のみ]

単なるデータ報告ではなく、経済学者としての深い洞察と理論的裏付けのある総合分析を提供してください。時系列データの変化と時事ニュースの両方を考慮した、包括的な経済情勢の評価を行ってください。Twitter投稿用サマリーでは、具体的な数値を使って人々の関心を引くような魅力的な表現を心がけ、専門家としての感想や驚きを率直に表現してください。

## 重要な注意事項：
- 時事ニュースの内容を積極的に分析に取り入れ、経済指標との関連性を具体的に説明
- 「提供データには不明な点が多いものの」「AIが分析した結果」などの分析プロセス言及は禁止
- 「推測される」「と思われる」「可能性がある」などの曖昧表現は避け、データに基づいた断定的分析
- 経済専門家として確信を持った分析を提供し、感想や驚き、関心を率直に表現
- 数値は正確に引用し、専門的で権威ある表現を使用
- ニュースとの関連性を明確に示し、総合的な経済情勢分析を提供
- データ品質や技術的な問題については言及せず、分析内容とニュースに集中
- 「〜に注目」「〜を示唆」などの定型表現を避け、より具体的で感情のこもった表現を使用
"""
        return prompt
    
    def generate_tweet_content(self, analysis_result):
        """
        分析結果からTwitter投稿用コンテンツを抽出（130-140文字制限対応）

        Args:
            analysis_result: 分析結果テキスト

        Returns:
            Twitter投稿用テキスト、エラーの場合はNone
        """
        try:
            # 正規表現で「Twitter投稿用サマリー」セクションを抽出
            match = re.search(r"### Twitter投稿用サマリー\n(.+?)(?=\n\n|$)", analysis_result, re.DOTALL)
            if match:
                tweet_content = match.group(1).strip()
                # 130-140文字制限を確認（ハッシュタグ分を考慮）
                max_content_length = 140
                if len(tweet_content) <= max_content_length:
                    return tweet_content
                else:
                    # 140文字を超える場合はスマートに切り詰め
                    return self._truncate_smartly(tweet_content, max_content_length)
            
            # 「🔎Economistの視点：」で始まる投稿用サマリーを探す
            if "🔎Economistの視点：" in analysis_result:
                start_idx = analysis_result.find("🔎Economistの視点：")
                if start_idx != -1:
                    # その行の終わりまでを取得
                    end_idx = analysis_result.find("\n", start_idx)
                    if end_idx == -1:
                        end_idx = len(analysis_result)
                    tweet_content = analysis_result[start_idx:end_idx].strip()
                    max_content_length = 140
                    if len(tweet_content) <= max_content_length:
                        return tweet_content
                    else:
                        return self._truncate_smartly(tweet_content, max_content_length)
            
            # フォールバック：データセット名から始まる投稿用サマリーを探す
            economic_indicators = [
                "失業率", "物価指数", "生産指数", "機械受注", "消費支出", "求人倍率",
                "賃金", "輸出", "輸入", "景気動向", "企業物価", "住宅着工", "小売"
            ]
            
            for indicator in economic_indicators:
                if indicator in analysis_result:
                    # 指標名を含む行を探す
                    lines = analysis_result.split('\n')
                    for line in lines:
                        if indicator in line and len(line.strip()) > 20:  # ある程度の長さがある行
                            tweet_content = line.strip()
                            max_content_length = 140
                            if len(tweet_content) <= max_content_length:
                                return tweet_content
                            else:
                                return self._truncate_smartly(tweet_content, max_content_length)
            
            # 旧プロンプト用のフォールバック：「🔍経済学者の視点」を探す
            if "🔍経済学者の視点" in analysis_result:
                start_idx = analysis_result.find("🔍経済学者の視点")
                if start_idx != -1:
                    end_idx = analysis_result.find("\n", start_idx)
                    if end_idx == -1:
                        end_idx = len(analysis_result)
                    tweet_content = analysis_result[start_idx:end_idx].strip()
                    max_content_length = 140
                    if len(tweet_content) <= max_content_length:
                        return tweet_content
                    else:
                        return self._truncate_smartly(tweet_content, max_content_length)

            # 旧プロンプト用のフォールバック
            if "📊日本経済レポート" in analysis_result:
                start_idx = analysis_result.find("📊日本経済レポート")
                if start_idx != -1:
                    end_idx = analysis_result.find("\n", start_idx)
                    if end_idx == -1:
                        end_idx = len(analysis_result)
                    tweet_content = analysis_result[start_idx:end_idx].strip()
                    max_content_length = 140
                    if len(tweet_content) <= max_content_length:
                        return tweet_content
                    else:
                        return self._truncate_smartly(tweet_content, max_content_length)
            
            # それでも見つからない場合は、分析結果の最初の段落を使用
            logger.warning("Twitter投稿用サマリーセクションが見つかりません。分析結果の最初の段落を使用します。")
            first_paragraph = analysis_result.split("\n\n")[0].strip()
            max_content_length = 140
            if len(first_paragraph) <= max_content_length:
                return first_paragraph
            else:
                return self._truncate_smartly(first_paragraph, max_content_length)
                
        except Exception as e:
            logger.error("Twitter投稿コンテンツ生成エラー: %s", e)
            return None
    
    def _truncate_smartly(self, content, max_length):
        """
        コンテンツをスマートに切り詰める
        
        Args:
            content: 元のコンテンツ
            max_length: 最大文字数
            
        Returns:
            調整されたコンテンツ
        """
        if len(content) <= max_length:
            return content
        
        # 句読点での切り詰めを試す
        for delimiter in ["。", "！", "？", ".", "!", "?"]:
            last_delimiter = content[:max_length - 3].rfind(delimiter)
            if last_delimiter > max_length * 0.6:  # 60%以上の長さを保持
                return content[:last_delimiter + 1]
        
        # 句読点が見つからない場合は、単語境界で切り詰める
        return content[:max_length - 3] + "..."

    def _is_realistic_value(self, indicator_name, value):
        """
        指標値が現実的かどうかを判定

        Args:
            indicator_name: 指標名
            value: 値

        Returns:
            現実的な値かどうか
        """
        try:
            if value is None:
                return False

            val = float(value)

            # 失業率: 1-5%が現実的
            if "失業率" in indicator_name:
                return 1.0 <= val <= 5.0

            # 有効求人倍率: 0.5-2.0倍が現実的
            elif "有効求人倍率" in indicator_name:
                return 0.5 <= val <= 2.0

            # 物価指数: 80-150が現実的（2020年=100基準）
            elif "物価指数" in indicator_name:
                return 80.0 <= val <= 150.0

            # 生産指数: 80-120が現実的（2020年=100基準）
            elif "生産指数" in indicator_name or "鉱工業" in indicator_name:
                return 80.0 <= val <= 120.0

            # その他の指数: 50-200が現実的
            elif "指数" in indicator_name:
                return 50.0 <= val <= 200.0

            # 金額系: 負の値や極端に大きい値を除外
            elif "円" in str(indicator_name) or "額" in indicator_name:
                return 0 <= val <= 1000000  # 100万以下

            # その他: 極端な値を除外
            else:
                return -1000 <= val <= 100000

        except (ValueError, TypeError):
            return False

    def _generate_fallback_analysis(self, data_list, news_context=""):
        """
        Gemini APIが失敗した場合のフォールバック分析を生成（ニュース統合版）

        Args:
            data_list: 経済指標データのリスト
            news_context: ニュースコンテキスト

        Returns:
            フォールバック分析結果
        """
        try:
            logger.info("フォールバック分析を実行中（ニュース統合版）...")

            if not data_list:
                import random
                default_patterns = [
                    "🔎Economistの視点：日本経済の主要指標が示す変化に専門家として強い関心を持つ。構造的転換期の兆候を感じる",
                    "🔎Economistの視点：経済データの動向から日本経済の底力と課題が同時に見えてくる。今後の政策運営に注目",
                    "🔎Economistの視点：最新の経済指標が描く全体像から、持続的成長への道筋を探る重要な局面に差し掛かっている",
                    "🔎Economistの視点：経済統計の変化が示唆する日本経済の新たなステージ。専門家としても予想以上に興味深い展開"
                ]
                return random.choice(default_patterns)

            # ニュースから経済キーワードを抽出
            economic_keywords = self._extract_economic_keywords(news_context)

            # 単一指標の場合の分析
            if len(data_list) == 1:
                data = data_list[0]
                name = data.get('name', '経済指標')
                value = data.get('value')
                unit = data.get('unit', '')
                year = data.get('year', '2024')
                month = data.get('month', '')

                # 値の検証と現実性チェック
                if value is None or value == 'N/A':
                    logger.warning("フォールバック分析: 値が不明な指標 %s", name)
                    return self._create_news_integrated_analysis(name, year, economic_keywords)

                # 非現実的な値を除外
                if not self._is_realistic_value(name, value):
                    logger.warning("フォールバック分析: 非現実的な値を検出 %s = %s", name, value)
                    return self._create_news_integrated_analysis(name, year, economic_keywords)

                # 月情報の処理
                if month and month != 'N/A' and str(month).strip():
                    try:
                        month_int = int(month)
                        if 1 <= month_int <= 12:
                            time_str = "%s年%d月" % (year, month_int)
                        else:
                            time_str = "%s年" % year
                    except:
                        time_str = "%s年" % year
                else:
                    time_str = "%s年" % year

                # ニュース統合分析コメント生成
                analysis_comment = self._generate_news_integrated_comment(name, value, unit, economic_keywords)

                # 値の表示形式を調整
                if isinstance(value, (int, float)):
                    if unit == '%':
                        value_str = "%.1f%s" % (value, unit)
                    else:
                        value_str = "%s%s" % (value, unit)
                else:
                    value_str = "%s%s" % (value, unit)

                return "🔎Economistの視点：%sの%s%sから、%s" % (time_str, name, value_str, analysis_comment)

            # 複数指標の場合（ニュース統合版）
            else:
                # 最も重要な指標を選択
                primary_data = None
                for data in data_list:
                    if data.get('importance') == 'high':
                        primary_data = data
                        break

                if not primary_data:
                    primary_data = data_list[0]

                name = primary_data.get('name', '経済指標')
                value = primary_data.get('value')
                unit = primary_data.get('unit', '')
                year = primary_data.get('year', '2024')

                # ニュース統合分析
                news_insight = self._generate_multi_indicator_news_analysis(data_list, economic_keywords)

                # 値の表示形式を調整
                if value is not None and value != 'N/A':
                    if isinstance(value, (int, float)):
                        if unit == '%':
                            value_str = "%.1f%s" % (value, unit)
                        else:
                            value_str = "%s%s" % (value, unit)
                    else:
                        value_str = "%s%s" % (value, unit)
                    return "🔎Economistの視点：%s年の%s%sなど複数指標が示す、%s" % (year, name, value_str, news_insight)
                else:
                    return "🔎Economistの視点：%s年の%sなど複数指標が示す、%s" % (year, name, news_insight)

        except Exception as e:
            logger.error("フォールバック分析エラー: %s", e)
            import random
            error_fallback_patterns = [
                "🔎Economistの視点：日本経済の主要指標が示す変化に専門家として注目。データの背景にある経済動向を読み解く必要がある",
                "🔎Economistの視点：経済統計の動きから日本経済の構造変化を感じ取れる。今後の展開が非常に興味深い局面",
                "🔎Economistの視点：最新の経済データが描く全体像から、日本経済の潜在力と課題が浮き彫りになっている",
                "🔎Economistの視点：経済指標の変化が示唆する新たなトレンド。政策効果と市場の反応に注目が集まる"
            ]
            return random.choice(error_fallback_patterns)

    def _extract_economic_keywords(self, news_context):
        """
        ニュースコンテキストから経済キーワードを抽出

        Args:
            news_context: ニュースコンテキスト文字列

        Returns:
            抽出された経済キーワードのリスト
        """
        if not news_context:
            return []

        economic_terms = [
            '金利', '物価', '雇用', '失業', '賃金', '消費', '投資', '輸出', '輸入',
            '生産', '製造業', '建設', '住宅', '株価', '円安', '円高', 'インフレ',
            'デフレ', 'GDP', '景気', '経済成長', '企業業績', '設備投資'
        ]

        found_keywords = []
        for term in economic_terms:
            if term in news_context:
                found_keywords.append(term)

        return found_keywords[:3]  # 最大3つまで

    def _create_news_integrated_analysis(self, indicator_name, year, economic_keywords):
        """
        ニュース統合分析を作成

        Args:
            indicator_name: 指標名
            year: 年
            economic_keywords: 経済キーワードリスト

        Returns:
            ニュース統合分析文
        """
        import random

        if economic_keywords:
            keyword_text = "、".join(economic_keywords)
            news_integrated_patterns = [
                "🔎Economistの視点：%s年の%sが%s動向と密接に連動しており、経済政策の効果が数値に現れている。市場の反応も注目される",
                "🔎Economistの視点：%s年の%sに%s情勢の影響が色濃く反映され、日本経済の構造変化が加速している印象を受ける",
                "🔎Economistの視点：%s年の%sが%s関連の動きと歩調を合わせ、経済全体の方向性がより明確になってきた",
                "🔎Economistの視点：%s年の%sから%s動向との相関関係が読み取れ、今後の経済運営に重要な示唆を与えている"
            ]
            return random.choice(news_integrated_patterns) % (year, indicator_name, keyword_text)
        else:
            standalone_patterns = [
                "🔎Economistの視点：%s年の%sが示す数値から、日本経済の底力と課題が同時に見えてくる興味深い展開",
                "🔎Economistの視点：%s年の%sの動向に経済専門家として強い関心を持つ。今後の政策対応が鍵となる",
                "🔎Economistの視点：%s年の%sが描く経済の姿から、持続的成長への道筋を探る重要な手がかりを得られる",
                "🔎Economistの視点：%s年の%sの変化が日本経済の新たな局面を予感させる。構造改革の成果に期待"
            ]
            return random.choice(standalone_patterns) % (year, indicator_name)

    def _generate_news_integrated_comment(self, name, value, unit, economic_keywords):
        """
        ニュース統合コメントを生成

        Args:
            name: 指標名
            value: 値
            unit: 単位
            economic_keywords: 経済キーワードリスト

        Returns:
            ニュース統合コメント
        """
        import random

        # より具体的で感想を交えた分析コメント生成
        base_comments = []

        if '失業率' in name:
            if isinstance(value, (int, float)) and value < 3.0:
                base_comments = [
                    "労働市場の逼迫感が強まり、賃金上昇圧力が高まっている。企業の人材確保競争が激化する中、働き手にとって追い風の状況",
                    "完全雇用に近い水準で推移し、日本経済の底堅さを実感。ただし人手不足による生産性向上の必要性も浮き彫りに",
                    "雇用環境の好転が消費マインドを押し上げ、内需拡大への期待が高まる。労働者の交渉力向上も注目される"
                ]
            elif isinstance(value, (int, float)) and value > 4.0:
                base_comments = [
                    "雇用情勢の悪化が懸念される水準。政府の雇用対策と企業の雇用維持努力が試される局面",
                    "労働市場の調整圧力が強まり、構造的な雇用問題への対応が急務。職業訓練や再就職支援の重要性が増している",
                    "失業率上昇が消費者心理に与える影響を注視。経済政策の効果的な発動が求められる"
                ]
            else:
                base_comments = [
                    "雇用情勢の安定が続く中、質の高い雇用創出への転換期。働き方改革の成果が問われる",
                    "労働市場のバランスが保たれ、持続可能な雇用環境の構築が進む。スキルアップ支援の充実が鍵",
                    "雇用の量的安定から質的向上へのシフトが重要。多様な働き方への対応が企業に求められる"
                ]
        elif '物価' in name or '指数' in name:
            base_comments = [
                "物価動向が金融政策の方向性を左右する重要な局面。日銀の政策判断に市場の注目が集まる",
                "インフレ圧力の変化が家計の実質所得に直結。消費者の購買行動への影響を慎重に見極める必要",
                "価格転嫁の動きが企業収益と消費者負担のバランスを問う。適正な価格形成メカニズムの確立が課題"
            ]
        elif '生産' in name:
            base_comments = [
                "製造業の生産動向が日本経済の実力を映す鏡。グローバル競争力の維持・向上が問われる",
                "生産活動の変化が雇用や設備投資に波及。産業構造の転換期における企業の戦略的判断が重要",
                "デジタル化・自動化の進展が生産性向上の鍵。イノベーション投資の成果が試される"
            ]
        elif '輸出' in name or '輸入' in name:
            base_comments = [
                "貿易動向が円相場と密接に連動し、企業の収益構造に大きな影響。為替ヘッジ戦略の重要性が増す",
                "グローバルサプライチェーンの再構築が進む中、日本企業の競争力が試される。付加価値の高い製品・サービスへのシフトが急務",
                "地政学リスクが貿易パターンを変化させる中、新たな市場開拓と供給網の多様化が企業の生存戦略"
            ]
        elif '住宅' in name:
            base_comments = [
                "住宅市場の動向が内需の先行指標として注目。金利環境と人口動態の変化が市場を左右",
                "住宅投資の変化が建設業界から関連産業まで幅広く波及。地域経済への影響も無視できない",
                "住宅需要の質的変化が進む中、環境配慮型住宅やスマートホームへの転換が加速"
            ]
        elif '機械受注' in name:
            base_comments = [
                "設備投資の先行指標として企業の将来への期待を反映。デジタル投資の拡大が成長の鍵",
                "企業の投資意欲が経済の持続的成長を左右。生産性向上への投資が競争力の源泉",
                "機械受注の動向が製造業の設備更新サイクルを示唆。技術革新への対応が企業の明暗を分ける"
            ]
        else:
            base_comments = [
                "経済指標の変化が日本経済の新たな局面を示唆。構造改革の成果が問われる重要な時期",
                "マクロ経済の動向が企業戦略と家計行動に与える影響を注視。政策対応の効果が試される",
                "経済データが示すトレンドから、日本経済の潜在力と課題が浮き彫りに。持続的成長への道筋を探る"
            ]

        # ランダムに選択してバリエーションを確保
        base_comment = random.choice(base_comments)

        # ニュースキーワードとの関連付けをより具体的に
        if economic_keywords:
            relevant_keywords = []
            for keyword in economic_keywords:
                if (('雇用' in keyword or '失業' in keyword or '賃金' in keyword) and ('失業率' in name or '雇用' in name)) or \
                   (('物価' in keyword or 'インフレ' in keyword or 'デフレ' in keyword) and ('物価' in name or '指数' in name)) or \
                   (('生産' in keyword or '製造' in keyword) and '生産' in name) or \
                   (('輸出' in keyword or '輸入' in keyword or '円安' in keyword or '円高' in keyword) and ('輸出' in name or '輸入' in name)) or \
                   (('住宅' in keyword or '建設' in keyword) and '住宅' in name):
                    relevant_keywords.append(keyword)

            if relevant_keywords:
                keyword_text = "、".join(relevant_keywords)
                news_reactions = [
                    "最近の%s報道との整合性が高く、市場予想を裏付ける結果",
                    "%s動向と歩調を合わせた展開で、経済政策の効果が現れている",
                    "%s関連ニュースが示唆していた通りの動きで、今後の展開に注目",
                    "%s情勢を反映した数値として、専門家の見方が一致"
                ]
                news_reaction = random.choice(news_reactions) % keyword_text
                return "%s。%s。" % (base_comment, news_reaction)

        return "%s。" % base_comment

    def _generate_multi_indicator_news_analysis(self, data_list, economic_keywords):
        """
        複数指標のニュース統合分析を生成

        Args:
            data_list: 指標データリスト
            economic_keywords: 経済キーワードリスト

        Returns:
            複数指標ニュース統合分析
        """
        import random

        # より具体的で感想を交えた複数指標分析
        if economic_keywords:
            keyword_text = "、".join(economic_keywords)
            multi_analysis_patterns = [
                "最新の%s動向が複数の経済指標に同時に影響を与えており、政策効果の波及が確認できる。相互連関の強さに改めて驚かされる",
                "%s情勢の変化が経済全体に与える影響の大きさを実感。各指標の動きから日本経済の底力と課題が同時に見えてくる",
                "%s関連の動きが経済指標群に一貫したシグナルを送っており、市場の方向性がより明確になってきた印象",
                "複数指標が%s動向と連動する様子から、日本経済の構造変化が本格化していることを強く感じる"
            ]
            return random.choice(multi_analysis_patterns) % keyword_text
        else:
            standalone_patterns = [
                "複数の経済指標が示す一貫したトレンドから、日本経済が新たなステージに入ったことを実感。今後の展開が非常に興味深い",
                "各指標の動きを総合すると、日本経済の潜在力の高さと同時に構造的課題も浮き彫りに。バランスの取れた政策運営が重要",
                "経済指標群が描く全体像から、日本経済の回復力と適応力の強さを改めて認識。持続的成長への期待が高まる",
                "複数指標の同時分析により、日本経済の多面性と複雑さが鮮明に。専門家としても予想以上の興味深い展開"
            ]
            return random.choice(standalone_patterns)

def main():
    """
    メイン関数（テスト用）
    
    この関数は、`main.py`から呼び出されることを想定しており、
    単体で実行する際には環境変数をロードする必要があります。
    """
    from dotenv import load_dotenv
    load_dotenv(dotenv_path=os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".env"))
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        logger.error("GEMINI_API_KEYが設定されていません")
        return
    
    # テストデータ
    test_data = [
        {
            "name": "GDP",
            "description": "国内総生産",
            "value": "540.2",
            "unit": "兆円",
            "time": "2024-09-01",
            "frequency": "quarterly"
        },
        {
            "name": "失業率",
            "description": "完全失業率",
            "value": "2.5",
            "unit": "%",
            "time": "2024-11-01",
            "frequency": "monthly"
        }
    ]
    
    # アナライザー初期化
    analyzer = GeminiAnalyzer(api_key)
    
    # 分析実行
    analysis = analyzer.analyze_economic_data(test_data)
    
    if analysis:
        print("=== 分析結果 ===")
        print(analysis)
        print("\n=== Twitter投稿用コンテンツ ===")
        tweet_content = analyzer.generate_tweet_content(analysis)
        if tweet_content:
            print(tweet_content)
            print("\n文字数: %d" % len(tweet_content))
        else:
            print("Twitter投稿用コンテンツの生成に失敗しました")
    else:
        print("分析に失敗しました")

if __name__ == "__main__":
    main()

