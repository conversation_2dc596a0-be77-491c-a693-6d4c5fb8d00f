#!/usr/bin/env python3
"""
統計データ可視化モジュール
e-Stat統計データから美しいグラフ・チャートを生成
グラフタイプの自動選択と多様化機能付き
"""

import os
import logging
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.font_manager as fm
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
# from typing import List, Dict, Optional, Tuple

# 日本語フォント設定の強化
def setup_japanese_fonts():
    """日本語フォントの設定を強化"""
    try:
        # japanize_matplotlibを試す
        import japanize_matplotlib
        logger.info("japanize_matplotlib を使用して日本語フォントを設定")
        return True
    except ImportError:
        pass

    # システムの日本語フォントを検索
    japanese_fonts = [
        'IPAexGothic', 'IPAPGothic', 'IPAGothic', 'IPAexMincho', 'IPAMincho',
        'Hiragino Sans', 'Hiragino Kaku Gothic Pro', 'Yu Gothic', 'Meiryo',
        'MS Gothic', 'MS Mincho', 'Takao Gothic', 'VL PGothic', 'Noto Sans CJK JP'
    ]

    available_fonts = [f.name for f in fm.fontManager.ttflist]

    for font in japanese_fonts:
        if font in available_fonts:
            plt.rcParams['font.family'] = font
            logger.info("日本語フォントを設定: %s", font)
            return True

    # フォールバック: DejaVu Sans（英数字のみ）
    plt.rcParams['font.family'] = 'DejaVu Sans'
    logger.warning("日本語フォントが見つかりません。DejaVu Sansを使用")
    return False

# ログ設定
logger = logging.getLogger(__name__)

class ChartGenerator:
    """統計データ可視化クラス"""
    
    def __init__(self):
        """初期化"""
        # チャート保存ディレクトリの設定
        self.charts_dir = "charts"
        if not os.path.exists(self.charts_dir):
            os.makedirs(self.charts_dir)

        # 日本語フォント設定の強化
        self.japanese_font_available = setup_japanese_fonts()

        # スタイル設定
        try:
            plt.style.use('seaborn-v0_8')
        except OSError:
            try:
                plt.style.use('seaborn')
            except OSError:
                plt.style.use('default')
        sns.set_palette("husl")

        # 日本語フォント設定の詳細
        if self.japanese_font_available:
            plt.rcParams['font.family'] = ['IPAexGothic', 'IPAPGothic', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', 'Takao', 'VL PGothic', 'Noto Sans CJK JP']
            plt.rcParams['font.sans-serif'] = ['IPAexGothic', 'IPAPGothic', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', 'Takao', 'VL PGothic', 'Noto Sans CJK JP']
        else:
            plt.rcParams['font.family'] = ['DejaVu Sans']
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

        plt.rcParams['axes.unicode_minus'] = False  # マイナス記号の文字化け防止
        plt.rcParams['figure.figsize'] = (12, 8)  # 適切なサイズに調整
        plt.rcParams['font.size'] = 12  # 基本フォントサイズ
        plt.rcParams['axes.grid'] = True
        plt.rcParams['grid.alpha'] = 0.3

        # 各要素のフォントサイズ設定（バランス重視）
        plt.rcParams['axes.titlesize'] = 72      # タイトル（3倍に拡大）
        plt.rcParams['axes.labelsize'] = 16      # 軸ラベル
        plt.rcParams['xtick.labelsize'] = 14     # X軸目盛りラベル
        plt.rcParams['ytick.labelsize'] = 14     # Y軸目盛りラベル
        plt.rcParams['legend.fontsize'] = 12     # 凡例
        plt.rcParams['figure.titlesize'] = 84    # 図全体のタイトル（3倍に拡大）

        # 出力ディレクトリ
        self.output_dir = "charts"
        os.makedirs(self.output_dir, exist_ok=True)

        # カラーパレット
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#F18F01',
            'success': '#C73E1D',
            'info': '#5D737E',
            'light': '#F5F5F5',
            'dark': '#2C3E50',
            'warning': '#FF9500',
            'danger': '#FF3B30'
        }

        # グラフタイプ選択の履歴管理
        self.chart_type_history = []
    
    def create_time_series_chart(self, data_list, title="経済指標推移"):
        """
        時系列チャートを作成
        
        Args:
            data_list: 統計データのリスト
            title: チャートタイトル
            
        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(16, 10))
            
            # データを指標別にグループ化
            indicator_data = {}
            for data in data_list:
                indicator_name = data.get('name', 'Unknown')
                if indicator_name not in indicator_data:
                    indicator_data[indicator_name] = {
                        'dates': [],
                        'values': [],
                        'unit': data.get('unit', ''),
                        'category': data.get('category', '')
                    }

                # 履歴データから時系列を構築
                historical_data = data.get('historical_data', [])
                if historical_data:
                    for hist_item in historical_data:
                        date_str = hist_item.get('@time', '')
                        value = hist_item.get('$')

                        # NaTや無効な日付をスキップ
                        if date_str and value is not None and str(date_str) != 'NaT' and not pd.isna(date_str):
                            try:
                                # 複数の日付フォーマットを試す
                                date_obj = None
                                date_str_clean = str(date_str).strip()

                                for fmt in ['%Y%m%d', '%Y%m', '%Y', '%Y/%m/%d', '%Y-%m-%d', '%Y-%m']:
                                    try:
                                        date_obj = datetime.strptime(date_str_clean, fmt)
                                        break
                                    except ValueError:
                                        continue

                                if date_obj and not pd.isna(value):
                                    try:
                                        float_value = float(value)
                                        # 現実的な値のみを追加
                                        if self._is_realistic_chart_value(indicator_name, float_value):
                                            indicator_data[indicator_name]['dates'].append(date_obj)
                                            indicator_data[indicator_name]['values'].append(float_value)
                                        else:
                                            logger.warning("非現実的な値をスキップ: %s = %s", indicator_name, float_value)
                                    except (ValueError, TypeError):
                                        continue
                            except (ValueError, TypeError):
                                continue
                else:
                    # 履歴データがない場合は現在の値のみ使用
                    date_str = data.get('time', '')
                    value = data.get('value')

                    if value is not None:
                        try:
                            # 現在の日付を使用
                            date_obj = datetime.now()
                            indicator_data[indicator_name]['dates'].append(date_obj)
                            indicator_data[indicator_name]['values'].append(float(value))
                        except (ValueError, TypeError):
                            continue
            
            # 各指標をプロット
            color_idx = 0
            colors_list = list(self.colors.values())

            for indicator_name, data_info in indicator_data.items():
                if len(data_info['dates']) > 0:
                    color = colors_list[color_idx % len(colors_list)]

                    # データをソート
                    sorted_data = sorted(zip(data_info['dates'], data_info['values']))
                    dates, values = zip(*sorted_data)

                    # プロット（線を確実に表示）
                    if len(dates) >= 2:
                        # 複数データポイントがある場合は必ず線グラフ
                        logger.info("複数指標折れ線グラフ描画: %s (%d点)", indicator_name, len(dates))

                        line = ax.plot(dates, values,
                                      linestyle='-',        # 実線を明示
                                      marker='o',           # 円形マーカー
                                      linewidth=3,          # 太い線
                                      markersize=6,         # 適度なマーカー
                                      color=color,
                                      markerfacecolor='white',
                                      markeredgecolor=color,
                                      markeredgewidth=2,
                                      label="%s (%s)" % (indicator_name, data_info['unit']),
                                      alpha=1.0,            # 完全不透明
                                      zorder=10)            # 最前面に表示

                        if line:
                            logger.info("複数指標線グラフ作成成功: %s", indicator_name)
                        else:
                            logger.error("複数指標線グラフ作成失敗: %s", indicator_name)

                    elif len(dates) == 1:
                        # 単一データポイントの場合はマーカーのみ
                        ax.scatter(dates, values,
                                  s=100,
                                  color=color,
                                  alpha=1.0,
                                  edgecolors='white',
                                  linewidth=2,
                                  label="%s (%s)" % (indicator_name, data_info['unit']),
                                  zorder=10)
                        logger.info("複数指標単一ポイント表示: %s", indicator_name)
                    else:
                        logger.warning("複数指標でデータポイントが0個: %s", indicator_name)

                    color_idx += 1

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=20, fontweight='bold', pad=20)
            ax.set_xlabel('日付', fontsize=14, fontweight='bold')
            ax.set_ylabel('値', fontsize=14, fontweight='bold')

            # 日付軸の設定（短縮フォーマット）
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.xticks(rotation=45, fontsize=12, ha='right')
            plt.yticks(fontsize=12)

            # 凡例（画面内に配置）
            ax.legend(loc='upper left', fontsize=10, framealpha=0.9)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--')

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.15, left=0.1, right=0.95, top=0.9)
            
            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "timeseries_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)
            
            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info("時系列チャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("時系列チャート作成エラー: %s", e)
            plt.close()
            return None
    
    def create_comparison_chart(self, data_list, title="経済指標比較"):
        """
        比較バーチャートを作成
        
        Args:
            data_list: 統計データのリスト
            title: チャートタイトル
            
        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # データの準備
            indicators = []
            values = []
            colors_list = []
            
            for i, data in enumerate(data_list[:8]):  # 最大8個まで
                name = data.get('name', 'Unknown')
                value = data.get('value')
                unit = data.get('unit', '')
                
                if value is not None:
                    indicators.append("{name}\n(%s)" % (unit,))
                    values.append(value)
                    colors_list.append(list(self.colors.values())[i % len(self.colors)])
            
            if not values:
                logger.warning("比較チャート用のデータがありません")
                return None
            
            # チャート作成
            fig, ax = plt.subplots(figsize=(16, 10))

            bars = ax.bar(indicators, values, color=colors_list, alpha=0.8, edgecolor='white', linewidth=2)

            # 値をバーの上に表示（大きなフォント）
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                       '%.2f' % value, ha='center', va='bottom', fontweight='bold', fontsize=16)

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=18, fontweight='bold', pad=15)
            ax.set_ylabel('値', fontsize=14, fontweight='bold')

            # X軸ラベルの回転と適切なフォント
            plt.xticks(rotation=45, ha='right', fontsize=11)
            plt.yticks(fontsize=11)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.2, left=0.12, right=0.95, top=0.9)
            
            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "comparison_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)
            
            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info("比較チャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("比較チャート作成エラー: %s", e)
            plt.close()
            return None
    
    def create_trend_analysis_chart(self, data_list, title="トレンド分析"):
        """
        トレンド分析チャートを作成（変化率を可視化）
        
        Args:
            data_list: 統計データのリスト
            title: チャートタイトル
            
        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # 変化率データの準備
            indicators = []
            change_rates = []
            colors_list = []

            for data in data_list:
                time_series_analysis = data.get('time_series_analysis', {})
                change_rate = time_series_analysis.get('change_rate')

                if change_rate is not None:
                    name = data.get('name', 'Unknown')
                    # 名前を短縮
                    if len(name) > 20:
                        name = name[:17] + "..."
                    indicators.append(name)
                    change_rates.append(change_rate)

                    # 色を変化率に応じて設定
                    if change_rate > 0:
                        colors_list.append(self.colors['success'])  # 上昇は赤
                    elif change_rate < 0:
                        colors_list.append(self.colors['primary'])  # 下降は青
                    else:
                        colors_list.append(self.colors['info'])     # 変化なしはグレー
            
            if not change_rates:
                logger.warning("トレンド分析チャート用のデータがありません")
                return None
            
            # チャート作成
            fig, ax = plt.subplots(figsize=(16, 10))

            bars = ax.barh(indicators, change_rates, color=colors_list, alpha=0.8, edgecolor='white', linewidth=2)

            # 0ラインを強調
            ax.axvline(x=0, color='black', linewidth=2, alpha=0.8)

            # 値をバーの端に表示（大きなフォント）
            for bar, rate in zip(bars, change_rates):
                width = bar.get_width()
                x_pos = width + (0.2 if width >= 0 else -0.2)
                ax.text(x_pos, bar.get_y() + bar.get_height()/2.,
                       '%+.1f%%' % rate, ha='left' if width >= 0 else 'right',
                       va='center', fontweight='bold', fontsize=16)

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=40)
            ax.set_xlabel('変化率 (%)', fontsize=22, fontweight='bold')

            # Y軸ラベルのフォントサイズ
            plt.yticks(fontsize=18)
            plt.xticks(fontsize=20)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='x')
            
            # レイアウト調整
            plt.tight_layout()
            
            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "trend_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)
            
            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info("トレンド分析チャート作成完了: %s", filepath)
            return filepath
            
        except Exception as e:
            logger.error("トレンド分析チャート作成エラー: %s", e)
            plt.close()
            return None
    
    def create_comprehensive_chart(self, data_list, title="経済指標総合分析"):
        """
        包括的な分析チャートを作成（複数のサブプロットを含む）
        
        Args:
            data_list: 統計データのリスト
            title: チャートタイトル
            
        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig = plt.figure(figsize=(20, 14))

            # サブプロット1: 最新値比較
            ax1 = plt.subplot(2, 2, 1)
            indicators = [data.get('name', 'Unknown')[:12] + '...' if len(data.get('name', '')) > 12
                         else data.get('name', 'Unknown') for data in data_list[:6]]
            values = [data.get('value', 0) for data in data_list[:6]]
            colors = [list(self.colors.values())[i % len(self.colors)] for i in range(len(values))]

            bars1 = ax1.bar(indicators, values, color=colors, alpha=0.8)
            ax1.set_title('最新値比較', fontweight='bold', fontsize=36)
            ax1.tick_params(axis='x', rotation=45, labelsize=16)
            ax1.tick_params(axis='y', labelsize=16)

            # 値をバーの上に表示
            for bar, value in zip(bars1, values):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                        '%.1f' % value, ha='center', va='bottom', fontweight='bold', fontsize=11)
            
            # サブプロット2: 変化率
            ax2 = plt.subplot(2, 2, 2)
            change_data = [(data.get('name', 'Unknown')[:12] + '...' if len(data.get('name', '')) > 12
                           else data.get('name', 'Unknown'),
                           data.get('trend_analysis', {}).get('change_rate', 0))
                          for data in data_list if data.get('trend_analysis', {}).get('change_rate') is not None]

            if change_data:
                names, rates = zip(*change_data[:6])
                colors2 = ['red' if r > 0 else 'blue' if r < 0 else 'gray' for r in rates]
                bars2 = ax2.barh(names, rates, color=colors2, alpha=0.8)
                ax2.set_title('変化率 (%)', fontweight='bold', fontsize=36)
                ax2.axvline(x=0, color='black', linewidth=1)
                ax2.tick_params(axis='both', labelsize=16)

                # 値をバーの端に表示
                for bar, rate in zip(bars2, rates):
                    width = bar.get_width()
                    x_pos = width + (0.1 if width >= 0 else -0.1)
                    ax2.text(x_pos, bar.get_y() + bar.get_height()/2.,
                            '%+.1f%%' % rate, ha='left' if width >= 0 else 'right',
                            va='center', fontweight='bold', fontsize=11)
            
            # サブプロット3: カテゴリ別分布
            ax3 = plt.subplot(2, 2, 3)
            categories = {}
            for data in data_list:
                cat = data.get('category', 'その他')
                categories[cat] = categories.get(cat, 0) + 1

            if categories:
                wedges, texts, autotexts = ax3.pie(categories.values(), labels=categories.keys(),
                                                  autopct='%1.1f%%', startangle=90, textprops={'fontsize': 12})
                ax3.set_title('カテゴリ別分布', fontweight='bold', fontsize=36)
                # パーセンテージのフォントサイズを調整
                for autotext in autotexts:
                    autotext.set_fontsize(12)
                    autotext.set_fontweight('bold')

            # サブプロット4: 重要度別
            ax4 = plt.subplot(2, 2, 4)
            importance_data = {}
            for data in data_list:
                imp = data.get('importance', 'medium')
                importance_data[imp] = importance_data.get(imp, 0) + 1

            if importance_data:
                imp_colors = {'high': 'red', 'medium': 'orange', 'low': 'green'}
                colors4 = [imp_colors.get(imp, 'gray') for imp in importance_data.keys()]
                bars4 = ax4.bar(importance_data.keys(), importance_data.values(), color=colors4, alpha=0.8)
                ax4.set_title('重要度別分布', fontweight='bold', fontsize=36)
                ax4.tick_params(axis='both', labelsize=16)

                # 値をバーの上に表示
                for bar, value in zip(bars4, importance_data.values()):
                    height = bar.get_height()
                    ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                            '%s' % value, ha='center', va='bottom', fontweight='bold', fontsize=12)

            # 全体タイトル
            fig.suptitle(title, fontsize=52, fontweight='bold', y=0.98)
            
            # レイアウト調整
            plt.tight_layout()
            plt.subplots_adjust(top=0.93)
            
            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "comprehensive_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)
            
            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info("包括的分析チャート作成完了: %s", filepath)
            return filepath
            
        except Exception as e:
            logger.error("包括的分析チャート作成エラー: %s", e)
            plt.close()
            return None
    
    def create_pie_chart(self, data_list, title="経済指標構成比"):
        """
        円グラフを作成（構成比や割合データに適用）

        Args:
            data_list: 統計データのリスト
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # データの準備
            labels = []
            values = []
            colors_list = []

            for i, data in enumerate(data_list[:8]):  # 最大8個まで
                name = data.get('name', 'Unknown')
                value = data.get('value')

                if value is not None and value > 0:  # 正の値のみ
                    # 名前を短縮
                    if len(name) > 12:
                        name = name[:10] + "..."
                    labels.append(name)
                    values.append(abs(value))  # 絶対値を使用
                    colors_list.append(list(self.colors.values())[i % len(self.colors)])

            if not values:
                logger.warning("円グラフ用のデータがありません")
                return None

            # チャート作成
            fig, ax = plt.subplots(figsize=(14, 10))

            wedges, texts, autotexts = ax.pie(values, labels=labels, colors=colors_list,
                                            autopct='%1.1f%%', startangle=90,
                                            textprops={'fontsize': 14})

            # パーセンテージのフォントサイズを調整
            for autotext in autotexts:
                autotext.set_fontsize(16)
                autotext.set_fontweight('bold')
                autotext.set_color('white')

            # ラベルのフォントサイズを調整
            for text in texts:
                text.set_fontsize(16)
                text.set_fontweight('bold')

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)

            # 凡例を追加（画面内に配置）
            ax.legend(wedges, ["%s: %.1f" % (label, value) for label, value in zip(labels, values)],
                     title="指標値", loc="upper left", bbox_to_anchor=(1.05, 1),
                     fontsize=10)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(left=0.1, right=0.75, top=0.9, bottom=0.1)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "pie_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("円グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("円グラフ作成エラー: %s", e)
            plt.close()
            return None

    def create_line_chart(self, data_list, title="経済指標推移"):
        """
        折れ線グラフを作成（時系列データや推移に適用）

        Args:
            data_list: 統計データのリスト
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(16, 10))

            # データの準備
            indicators = []
            values = []
            colors_list = []

            for i, data in enumerate(data_list):
                name = data.get('name', 'Unknown')
                value = data.get('value')

                if value is not None:
                    # 名前を短縮
                    if len(name) > 15:
                        name = name[:12] + "..."
                    indicators.append(name)
                    values.append(value)
                    colors_list.append(list(self.colors.values())[i % len(self.colors)])

            if not values:
                logger.warning("折れ線グラフ用のデータがありません")
                return None

            # 折れ線グラフを作成
            x_positions = range(len(indicators))
            ax.plot(x_positions, values, marker='o', linewidth=4, markersize=10,
                   color=self.colors['primary'], alpha=0.8)

            # データポイントに値を表示
            for i, (x, y) in enumerate(zip(x_positions, values)):
                ax.annotate('%.1f' % y, (x, y), textcoords="offset points",
                           xytext=(0,15), ha='center', fontsize=16, fontweight='bold')

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=40)
            ax.set_xlabel('指標', fontsize=22, fontweight='bold')
            ax.set_ylabel('値', fontsize=22, fontweight='bold')

            # X軸の設定
            ax.set_xticks(x_positions)
            ax.set_xticklabels(indicators, rotation=45, ha='right', fontsize=18)
            plt.yticks(fontsize=20)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--')

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "line_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("折れ線グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("折れ線グラフ作成エラー: %s", e)
            plt.close()
            return None

    def determine_optimal_chart_type(self, data_list, single_indicator=False):
        """
        データの特性に基づいて最適なグラフタイプを自動判断

        Args:
            data_list: 統計データのリスト
            single_indicator: 単一指標かどうか

        Returns:
            dict: 推奨グラフタイプと理由
        """
        try:
            if not data_list:
                return {'type': 'simple_bar', 'reason': 'データが空のため、シンプルな棒グラフを選択'}

            # 単一指標の場合
            if single_indicator or len(data_list) == 1:
                return self._determine_single_indicator_chart_type(data_list[0] if data_list else {})

            # 複数指標の場合
            return self._determine_multiple_indicators_chart_type(data_list)

        except Exception as e:
            logger.error("グラフタイプ決定エラー: %s", e)
            return {'type': 'simple_bar', 'reason': 'エラーのため、フォールバック棒グラフを選択'}

    def _determine_single_indicator_chart_type(self, data):
        """単一指標のグラフタイプを決定"""
        try:
            indicator_name = data.get('name', '').lower()
            unit = str(data.get('unit', '')).lower()
            value = data.get('value')
            historical_data = data.get('historical_data', [])

            # 時系列データの詳細ログ出力
            logger.info("=== チャートタイプ決定 ===")
            logger.info("指標名: %s", indicator_name)
            logger.info("現在値: %s %s", value, unit)
            logger.info("時系列データ件数: %d件", len(historical_data))
            if len(historical_data) > 0:
                logger.info("時系列データサンプル: %s", historical_data[:3])

            # 多様化されたチャートタイプ選択ロジック
            import random

            # 有効な時系列データをカウント
            valid_time_series_count = self._count_valid_time_series_data(historical_data)

            logger.info("=== 多様化チャートタイプ選択 ===")
            logger.info("有効時系列データ: %d件", valid_time_series_count)

            # 指標の特性に基づいたチャートタイプ選択
            chart_selection_weights = {}

            # 時系列データが豊富な場合（5点以上）
            if valid_time_series_count >= 5:
                chart_selection_weights = {
                    'time_series': 40,  # 時系列チャート
                    'area': 25,         # エリアチャート
                    'line_with_markers': 20,  # マーカー付き線グラフ
                    'enhanced_bar': 10, # 拡張棒グラフ
                    'scatter': 5        # 散布図
                }
            # 中程度の時系列データ（3-4点）
            elif valid_time_series_count >= 3:
                chart_selection_weights = {
                    'time_series': 35,
                    'enhanced_bar': 25,
                    'area': 20,
                    'pie': 15,
                    'horizontal_bar': 5
                }
            # 少ない時系列データ（1-2点）
            elif valid_time_series_count >= 1:
                chart_selection_weights = {
                    'enhanced_bar': 30,
                    'pie': 25,
                    'horizontal_bar': 20,
                    'donut': 15,
                    'radar': 10
                }
            # 時系列データなし
            else:
                chart_selection_weights = {
                    'enhanced_bar': 35,
                    'pie': 30,
                    'donut': 20,
                    'horizontal_bar': 15
                }

            # 重み付きランダム選択
            chart_types = list(chart_selection_weights.keys())
            weights = list(chart_selection_weights.values())
            selected = random.choices(chart_types, weights=weights, k=1)[0]

            logger.info("✅ 多様化チャート選択: %s (重み: %s)", selected, chart_selection_weights[selected])
            return {'type': selected, 'reason': f'有効時系列データ{valid_time_series_count}件で{selected}選択（重み付き選択）'}



        except Exception as e:
            logger.error("単一指標グラフタイプ決定エラー: %s", e)
            return {'type': 'simple_bar', 'reason': 'エラーのため、フォールバック棒グラフを選択'}

    def _count_valid_time_series_data(self, historical_data):
        """
        有効な時系列データポイントの数をカウント

        Args:
            historical_data: 履歴データのリスト

        Returns:
            有効なデータポイントの数
        """
        try:
            if not historical_data:
                return 0

            valid_count = 0
            for data_point in historical_data:
                if isinstance(data_point, dict):
                    value = data_point.get('$')
                    if value is not None and not pd.isna(value):
                        try:
                            float(value)
                            valid_count += 1
                        except (ValueError, TypeError):
                            continue

            return valid_count

        except Exception as e:
            logger.error("時系列データ有効性チェックエラー: %s", e)
            return 0

    def _is_realistic_chart_value(self, indicator_name, value):
        """
        チャート用の現実的な値かどうかを判定

        Args:
            indicator_name: 指標名
            value: 値

        Returns:
            現実的な値かどうか
        """
        try:
            if value is None or pd.isna(value):
                return False

            val = float(value)

            # 失業率: 1-5%が現実的
            if "失業率" in indicator_name:
                return 1.0 <= val <= 5.0

            # 有効求人倍率: 0.5-2.0倍が現実的
            elif "有効求人倍率" in indicator_name:
                return 0.5 <= val <= 2.0

            # 物価指数: 80-150が現実的（2020年=100基準）
            elif "物価指数" in indicator_name:
                return 80.0 <= val <= 150.0

            # 生産指数: 80-120が現実的（2020年=100基準）
            elif "生産指数" in indicator_name or "鉱工業" in indicator_name:
                return 80.0 <= val <= 120.0

            # その他の指数: 50-200が現実的
            elif "指数" in indicator_name:
                return 50.0 <= val <= 200.0

            # 金額系: 負の値や極端に大きい値を除外
            elif "円" in str(indicator_name) or "額" in indicator_name:
                return 0 <= val <= 1000000  # 100万以下

            # その他: 極端な値を除外
            else:
                return -1000 <= val <= 100000

        except (ValueError, TypeError):
            return False

    def _determine_multiple_indicators_chart_type(self, data_list):
        """複数指標のグラフタイプを決定"""
        try:
            data_count = len(data_list)

            # 指標の特性分析
            indicator_names = [str(data.get('name', '')).lower() for data in data_list]
            combined_names = ' '.join(indicator_names)

            has_rates = sum('率' in name for name in indicator_names)
            has_indices = sum('指数' in name for name in indicator_names)
            has_amounts = sum('額' in name or '円' in str(data.get('unit', '')) for name, data in zip(indicator_names, data_list))

            # 変化率データの有無
            has_trend_data = sum(1 for data in data_list if data.get('time_series_analysis', {}).get('change_rate') is not None)

            # 時系列データの品質
            time_series_quality = sum(1 for data in data_list if len(data.get('historical_data', [])) >= 3)

            # 複数指標の判定ロジック
            if has_trend_data >= data_count * 0.7:  # 70%以上に変化率データがある
                return {'type': 'trend', 'reason': '複数指標で変化率データが豊富なため、トレンド分析チャートが最適'}
            elif time_series_quality >= data_count * 0.6:  # 60%以上に時系列データがある
                return {'type': 'time_series', 'reason': '複数指標で時系列データが豊富なため、時系列比較チャートが最適'}
            elif data_count >= 6:
                return {'type': 'comprehensive', 'reason': '指標数が多いため、包括的分析チャートで多面的表示'}
            elif has_rates >= data_count * 0.8:  # 80%以上が率系
                return {'type': 'comparison', 'reason': '率系指標が多いため、比較棒グラフが最適'}
            elif has_amounts >= data_count * 0.6:  # 60%以上が金額系
                if data_count <= 5:
                    return {'type': 'pie', 'reason': '金額系指標で少数のため、円グラフで構成比を表示'}
                else:
                    return {'type': 'comparison', 'reason': '金額系指標で多数のため、比較棒グラフが最適'}
            else:
                return {'type': 'comparison', 'reason': 'デフォルトとして比較棒グラフを選択'}

        except Exception as e:
            logger.error("複数指標グラフタイプ決定エラー: %s", e)
            return {'type': 'comparison', 'reason': 'エラーのため、フォールバック比較チャートを選択'}

    def create_chart_with_fallback(self, data, title="経済指標", chart_type_info=None):
        """
        時系列データ不足時の代替処理を含むチャート作成

        Args:
            data: 統計データ（単一または複数）
            title: チャートタイトル
            chart_type_info: グラフタイプ情報（determine_optimal_chart_typeの結果）

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # データが単一か複数かを判定
            if isinstance(data, list):
                is_single = len(data) == 1
                target_data = data[0] if is_single else data
            else:
                is_single = True
                target_data = data
                data = [data]  # リスト形式に統一

            # グラフタイプが指定されていない場合は自動判定
            if not chart_type_info:
                chart_type_info = self.determine_optimal_chart_type(data, single_indicator=is_single)

            chart_type = chart_type_info.get('type', 'simple_bar')
            reason = chart_type_info.get('reason', '自動選択')

            # グラフタイプ選択の理由をログ出力
            logger.info("選択されたグラフタイプ: %s", chart_type)
            logger.info("選択理由: %s", reason)

            # 履歴に記録
            self.chart_type_history.append({
                'timestamp': datetime.now(),
                'chart_type': chart_type,
                'reason': reason,
                'indicator_count': len(data)
            })

            # グラフタイプに応じてチャートを作成
            chart_path = None

            if chart_type == 'time_series':
                if is_single:
                    chart_path = self.create_single_indicator_time_series(target_data, title)
                else:
                    chart_path = self.create_time_series_chart(data, title)

            elif chart_type == 'gauge':
                chart_path = self.create_gauge_chart(target_data, title)

            elif chart_type == 'radar':
                chart_path = self.create_radar_chart(target_data, title)

            elif chart_type == 'donut':
                chart_path = self.create_donut_chart(target_data, title)

            elif chart_type == 'line_with_markers':
                chart_path = self.create_line_with_markers_chart(target_data, title)

            elif chart_type == 'scatter':
                # 時系列データの場合は散布図よりも線グラフが適切
                historical_data = target_data.get('historical_data', [])
                if len(historical_data) >= 3:
                    logger.info("時系列データ検出: 散布図から線グラフに変更")
                    chart_path = self.create_line_with_markers_chart(target_data, title)
                else:
                    chart_path = self.create_scatter_chart(target_data, title)

            elif chart_type == 'area':
                chart_path = self.create_area_chart(target_data, title)

            elif chart_type == 'enhanced_bar':
                chart_path = self.create_enhanced_bar_chart(target_data, title)

            elif chart_type == 'pie':
                if is_single:
                    chart_path = self.create_pie_chart(target_data, title)
                else:
                    chart_path = self.create_pie_chart(data, title)

            elif chart_type == 'comparison':
                chart_path = self.create_comparison_chart(data, title)

            elif chart_type == 'trend':
                chart_path = self.create_trend_analysis_chart(data, title)

            elif chart_type == 'comprehensive':
                chart_path = self.create_comprehensive_chart(data, title)

            else:  # 'simple_bar' or fallback
                chart_path = self.create_simple_bar_chart(target_data, title)

            # チャート作成に失敗した場合のフォールバック処理
            if not chart_path:
                logger.warning("メインチャート作成失敗。フォールバック処理を実行")
                chart_path = self._create_fallback_chart(target_data, title)

            return chart_path

        except Exception as e:
            logger.error("チャート作成エラー: %s", e)
            # 最終フォールバック
            return self._create_emergency_fallback_chart(target_data if 'target_data' in locals() else data, title)

    def _create_fallback_chart(self, data, title):
        """フォールバック用の適切なチャート作成（データ特性に応じて選択）"""
        try:
            # データの特性を分析
            chart_type = self._analyze_data_for_fallback_chart(data)
            logger.info("フォールバック処理: %s チャートを作成", chart_type)

            # データ特性に応じたチャート作成
            if chart_type == 'pie':
                return self.create_pie_chart(data, title)
            elif chart_type == 'donut':
                return self.create_donut_chart(data, title)
            elif chart_type == 'enhanced_bar':
                return self.create_enhanced_bar_chart(data, title)
            elif chart_type == 'horizontal_bar':
                return self.create_horizontal_bar_chart(data, title)
            elif chart_type == 'gauge':
                return self.create_gauge_chart(data, title)
            else:
                # デフォルトは拡張棒グラフ
                return self.create_enhanced_bar_chart(data, title)

        except Exception as e:
            logger.error("フォールバックチャート作成エラー: %s", e)
            return self._create_emergency_fallback_chart(data, title)

    def _analyze_data_for_fallback_chart(self, data):
        """データ特性を分析してフォールバック用の適切なチャートタイプを決定"""
        try:
            if isinstance(data, dict):
                indicator_name = data.get('name', '').lower()
                value = data.get('value')
                unit = data.get('unit', '').lower()
                historical_data = data.get('historical_data', [])

                # 指標名による判定
                if any(keyword in indicator_name for keyword in ['割合', '率', '比率', 'シェア']):
                    return 'pie'
                elif any(keyword in indicator_name for keyword in ['指数', 'インデックス']):
                    return 'gauge'
                elif any(keyword in indicator_name for keyword in ['地域', '都道府県', '県別']):
                    return 'horizontal_bar'
                elif any(keyword in indicator_name for keyword in ['業種', '産業', '分野']):
                    return 'donut'

                # 単位による判定
                if unit in ['%', '％', 'パーセント']:
                    return 'pie'
                elif unit in ['倍', '比']:
                    return 'gauge'

                # 履歴データの特性による判定
                if len(historical_data) > 0:
                    # 地域別データの場合
                    area_codes = set()
                    for item in historical_data[:10]:  # 最初の10件をチェック
                        if '@area' in item:
                            area_codes.add(item.get('@area'))

                    if len(area_codes) > 3:  # 複数地域のデータ
                        return 'horizontal_bar'

                    # カテゴリ別データの場合
                    categories = set()
                    for item in historical_data[:10]:
                        for key in ['@cat01', '@cat02', '@cat03']:
                            if key in item:
                                categories.add(item.get(key))

                    if len(categories) > 3:  # 複数カテゴリのデータ
                        return 'donut'

                # 値の範囲による判定
                if value is not None:
                    try:
                        val = float(value)
                        if 0 <= val <= 100 and unit in ['%', '％']:
                            return 'pie'
                        elif val > 1000000:  # 大きな値
                            return 'enhanced_bar'
                    except (ValueError, TypeError):
                        pass

                # デフォルト
                return 'enhanced_bar'

            else:
                # リストデータの場合
                return 'enhanced_bar'

        except Exception as e:
            logger.warning("データ特性分析エラー: %s", e)
            return 'enhanced_bar'

    def _create_emergency_fallback_chart(self, data, title):
        """緊急フォールバック: テキストベースの要約画像を作成"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.axis('off')

            # データの要約テキストを作成
            if isinstance(data, list):
                summary_text = "経済指標データ\n\n"
                for i, item in enumerate(data[:5]):  # 最大5個まで
                    name = item.get('name', 'Unknown')
                    value = item.get('value', 'N/A')
                    unit = item.get('unit', '')
                    summary_text += "%d. %s: %s%s\n" % (i+1, name, value, unit)
            else:
                name = data.get('name', 'Unknown')
                value = data.get('value', 'N/A')
                unit = data.get('unit', '')
                summary_text = "経済指標データ\n\n%s\n%s%s" % (name, value, unit)

            # テキストを表示
            ax.text(0.5, 0.5, summary_text, ha='center', va='center',
                   fontsize=16, fontweight='bold', transform=ax.transAxes,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))

            ax.set_title(title, fontsize=24, fontweight='bold', pad=20)

            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "emergency_fallback_%s.png" % timestamp
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("緊急フォールバックチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("緊急フォールバックチャート作成エラー: %s", e)
            return None

    def create_single_indicator_time_series(self, data, title="経済指標時系列分析"):
        """
        単一指標の時系列チャートを作成（直近5年分のデータ）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(16, 10))

            indicator_name = data.get('name', 'Unknown')
            unit = data.get('unit', '')

            logger.info("=== 時系列チャート生成開始 ===")
            logger.info("指標名: %s", indicator_name)

            # 履歴データから時系列を構築
            dates = []
            values = []
            historical_data = data.get('historical_data', [])

            logger.info("時系列データ件数: %d件", len(historical_data))

            # 時系列データの日付の多様性をチェック
            unique_dates = set()
            if historical_data:
                for hist_item in historical_data:
                    date_value = hist_item.get('@time', '')
                    value = hist_item.get('$')

                    if date_value and value is not None:
                        try:
                            date_obj = None

                            # Pandas Timestampオブジェクトの場合
                            if hasattr(date_value, 'to_pydatetime'):
                                date_obj = date_value.to_pydatetime()
                            elif hasattr(date_value, 'strftime'):
                                # datetime オブジェクトの場合
                                date_obj = date_value
                            else:
                                # 文字列の場合は複数の日付フォーマットを試す
                                date_str = str(date_value)
                                for fmt in ['%Y%m%d', '%Y%m', '%Y', '%Y/%m/%d', '%Y-%m-%d']:
                                    try:
                                        date_obj = datetime.strptime(date_str, fmt)
                                        break
                                    except ValueError:
                                        continue

                            if date_obj:
                                # 年月日の組み合わせで一意性をチェック
                                date_key = date_obj.strftime('%Y-%m-%d')
                                unique_dates.add(date_key)
                                dates.append(date_obj)
                                values.append(float(value))
                                logger.debug("時系列データ追加: %s = %s", date_obj.strftime('%Y/%m/%d'), value)
                        except (ValueError, TypeError) as e:
                            logger.debug("時系列データ解析エラー: %s = %s, エラー: %s", date_value, value, e)
                            continue

            logger.info("有効な時系列データ: %d件", len(dates))
            logger.info("異なる日付の数: %d件", len(unique_dates))

            # 真の時系列データかどうかを判定（異なる日付が3つ以上必要）
            if len(unique_dates) < 3:
                logger.warning("時系列データではありません（異なる日付: %d件）- 地域別データまたは単一時点データの可能性", len(unique_dates))
                logger.info("データ特性に応じた適切なチャートにフォールバック")
                return self._create_fallback_chart(data, title)

            # 現在の値を適切に追加（重複チェック付き）
            current_value = data.get('value')
            if current_value is not None:
                try:
                    current_val = float(current_value)

                    if dates:
                        # 既存データの最新日付を取得
                        latest_date = max(dates)

                        # 現在値が既存の最新値と異なる場合のみ追加
                        latest_value = values[dates.index(latest_date)]
                        if abs(current_val - latest_value) > 0.001:  # 微小な差は無視
                            # 月次データの場合は1ヶ月後、年次データの場合は1年後
                            try:
                                if len(dates) > 12:  # 月次データと推定
                                    if latest_date.month == 12:
                                        current_date = latest_date.replace(month=1, year=latest_date.year + 1)
                                    else:
                                        current_date = latest_date.replace(month=latest_date.month + 1)
                                else:  # 年次データと推定
                                    current_date = latest_date.replace(year=latest_date.year + 1)

                                dates.append(current_date)
                                values.append(current_val)
                                logger.info("現在値を追加: %s = %s", current_date.strftime('%Y/%m'), current_val)
                            except ValueError as e:
                                logger.warning("現在値の日付計算エラー: %s", e)
                    else:
                        # 履歴データがない場合は現在値のみ
                        dates = [datetime.now()]
                        values = [current_val]
                        logger.info("履歴データなし - 現在値のみ: %s", current_val)

                except (ValueError, TypeError) as e:
                    logger.warning("現在値の数値変換エラー: %s", e)

            # 時系列データが不足している場合は時系列チャートを作成しない
            if not dates or not values or len(dates) < 3:
                logger.warning("時系列データが不足 - 時系列チャート作成を中止: %s (データ件数: %d)", indicator_name, len(dates) if dates else 0)
                return None

            # データの重複除去とソート
            if dates and values:
                # 日付と値のペアを作成し、日付でソート
                data_pairs = list(zip(dates, values))

                # 重複する日付を除去（最新の値を保持、ただし異なる値は平均化）
                unique_data = {}
                for date, value in data_pairs:
                    if date in unique_data:
                        # 同じ日付で異なる値がある場合は平均を取る
                        existing_value = unique_data[date]
                        if isinstance(existing_value, list):
                            existing_value.append(value)
                        else:
                            unique_data[date] = [existing_value, value]
                    else:
                        unique_data[date] = value

                # リスト値を平均に変換
                for date in unique_data:
                    if isinstance(unique_data[date], list):
                        unique_data[date] = sum(unique_data[date]) / len(unique_data[date])

                # 日付でソート
                sorted_items = sorted(unique_data.items())
                dates, values = zip(*sorted_items) if sorted_items else ([], [])

                logger.info("重複除去後のデータ件数: %d件", len(dates))

                # 時系列データの期間を柔軟に設定（最大10年、最低3年）
                ten_years_ago = datetime.now() - timedelta(days=10*365)
                three_years_ago = datetime.now() - timedelta(days=3*365)

                # まず直近3年のデータを試行
                recent_data = [(d, v) for d, v in zip(dates, values) if d >= three_years_ago]

                if len(recent_data) >= 3:
                    dates, values = zip(*recent_data)
                    logger.info("直近3年のデータ件数: %d件", len(dates))
                elif len(dates) >= 3:
                    # 3年以内にデータが少ない場合は全データを使用
                    logger.info("全期間のデータを使用: %d件", len(dates))
                else:
                    logger.warning("時系列データが不足: %d件", len(dates))
            else:
                logger.warning("有効な時系列データがありません")

            # 折れ線グラフを作成（線を確実に表示）
            if len(dates) >= 2:
                # 複数データポイントがある場合は必ず線グラフ
                logger.info("=== 折れ線グラフ描画開始 ===")
                logger.info("データポイント数: %d", len(dates))
                logger.info("日付範囲: %s ～ %s", dates[0], dates[-1])
                logger.info("値の範囲: %.2f ～ %.2f", min(values), max(values))

                # 線グラフを明示的に描画
                line = ax.plot(dates, values,
                              linestyle='-',        # 実線を明示
                              marker='o',           # 円形マーカー
                              linewidth=4,          # 太い線
                              markersize=8,         # 大きなマーカー
                              color=self.colors['primary'],
                              alpha=1.0,            # 完全不透明
                              markerfacecolor='white',
                              markeredgecolor=self.colors['primary'],
                              markeredgewidth=3,
                              label="%s" % (indicator_name,),
                              zorder=10)            # 最前面に表示

                logger.info("折れ線グラフ描画完了: %d個のデータポイント", len(dates))

                # 線が描画されたことを確認
                if line:
                    logger.info("線オブジェクト作成成功: %s", type(line[0]))
                else:
                    logger.error("線オブジェクト作成失敗")

            elif len(dates) == 1:
                # 単一データポイントの場合は大きなマーカーで表示
                ax.scatter(dates, values, s=200, color=self.colors['primary'], alpha=1.0,
                          edgecolors='white', linewidth=3, label="%s" % (indicator_name,),
                          zorder=10)
                logger.info("単一ポイント表示: %d個のデータポイント", len(dates))
            else:
                logger.error("データポイントが0個です")

            # トレンドラインを追加
            if len(values) >= 3:
                # 線形回帰でトレンドを計算
                x_numeric = [(d - dates[0]).days for d in dates]
                z = np.polyfit(x_numeric, values, 1)
                p = np.poly1d(z)
                trend_values = [p(x) for x in x_numeric]

                ax.plot(dates, trend_values, '--', linewidth=3, alpha=0.7,
                       color=self.colors['accent'], label='トレンド')

            # 最新値を強調
            if dates and values:
                latest_date, latest_value = dates[-1], values[-1]
                ax.scatter([latest_date], [latest_value], s=200, color=self.colors['success'],
                          zorder=5, label='最新値: %.2f' % latest_value)

                # 最新値にラベルを追加
                ax.annotate('%.2f' % latest_value,
                           (latest_date, latest_value),
                           textcoords="offset points", xytext=(10,10),
                           ha='left', fontsize=18, fontweight='bold',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=18, fontweight='bold', pad=15)
            ax.set_xlabel('年月', fontsize=14, fontweight='bold')
            ax.set_ylabel('%s (%s)' % (indicator_name, unit), fontsize=14, fontweight='bold')

            # 日付軸の設定（短縮フォーマット）
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
            plt.xticks(rotation=45, fontsize=11, ha='right')
            plt.yticks(fontsize=11)

            # 凡例（コンパクトに）
            ax.legend(loc='upper left', fontsize=10, framealpha=0.9)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--')

            # Y軸の範囲を調整（データの変動を見やすくする）
            if values:
                y_min, y_max = min(values), max(values)
                y_range = y_max - y_min
                if y_range > 0:
                    ax.set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.15, left=0.12, right=0.95, top=0.9)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "timeseries_single_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("単一指標時系列チャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("単一指標時系列チャート作成エラー: %s", e)
            plt.close()
            return None

    def create_gauge_chart(self, data, title="経済指標"):
        """
        ゲージチャート（率系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            # ゲージの設定
            val = float(value)
            max_val = 100 if '%' in unit or '率' in indicator_name else max(val * 1.5, 100)

            # 角度計算（半円ゲージ）
            theta = np.linspace(0, np.pi, 100)
            r = np.ones_like(theta)

            # 背景ゲージ
            ax.plot(theta, r, color='lightgray', linewidth=20, alpha=0.3)

            # 値のゲージ
            value_theta = np.linspace(0, np.pi * (val / max_val), int(100 * val / max_val))
            value_r = np.ones_like(value_theta)

            # 色の決定
            if val <= max_val * 0.3:
                color = self.colors['success']
            elif val <= max_val * 0.7:
                color = self.colors['warning']
            else:
                color = self.colors['danger']

            ax.plot(value_theta, value_r, color=color, linewidth=20, alpha=0.8)

            # 針の描画
            needle_angle = np.pi * (val / max_val)
            ax.plot([needle_angle, needle_angle], [0, 0.8], color='black', linewidth=4)

            # 値の表示
            ax.text(np.pi/2, 0.5, '%s%s' % (val, unit), ha='center', va='center',
                   fontsize=32, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor='white', alpha=0.9))

            # 目盛りの追加
            for i in range(0, int(max_val) + 1, int(max_val/5)):
                angle = np.pi * (i / max_val)
                ax.plot([angle, angle], [0.9, 1.0], color='black', linewidth=2)
                ax.text(angle, 1.1, str(i), ha='center', va='center', fontsize=12)

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=60)
            ax.set_ylim(0, 1.2)
            ax.set_theta_zero_location('W')
            ax.set_theta_direction(1)
            ax.set_thetagrids([])
            ax.set_rgrids([])
            ax.spines['polar'].set_visible(False)

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "gauge_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("ゲージチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("ゲージチャート作成エラー: %s", e)
            plt.close()
            return None

    def create_pie_chart_single(self, data, title="経済指標"):
        """
        円グラフ（消費・支出系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 10))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # 比較値の設定（過去平均や参考値など）
            reference_value = val * 0.8  # 参考値
            remaining = max(0, reference_value - val) if val < reference_value else val * 0.2

            # データの準備
            if val >= reference_value:
                labels = ['現在値\n%s%s' % (val, unit), '上昇分\n%.1f%s' % (val - reference_value, unit)]
                sizes = [reference_value, val - reference_value]
                colors = [self.colors['primary'], self.colors['success']]
            else:
                labels = ['現在値\n%s%s' % (val, unit), '参考値との差\n%.1f%s' % (remaining, unit)]
                sizes = [val, remaining]
                colors = [self.colors['primary'], self.colors['secondary']]

            # 円グラフの作成
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                             startangle=90, textprops={'fontsize': 16, 'fontweight': 'bold'})

            # 中央に値を表示
            ax.text(0, 0, '%s%s' % (val, unit), ha='center', va='center',
                   fontsize=28, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "gauge_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("円グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("円グラフ作成エラー: %s", e)
            plt.close()
            return None

    def create_radar_chart(self, data, title="経済指標"):
        """
        レーダーチャート（物価・指数系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # レーダーチャートの軸（5つの評価軸を作成）
            categories = ['現在値', '前月比', '前年比', '平均値', '最高値']
            N = len(categories)

            # 角度の計算
            angles = [n / float(N) * 2 * np.pi for n in range(N)]
            angles += angles[:1]  # 円を閉じる

            # 値の正規化（動的スケール）
            max_scale = max(val * 1.2, 100)  # 現在値の1.2倍または100のうち大きい方
            values = [
                min(val / max_scale * 100, 100),  # 現在値
                75,  # 前月比（仮値）
                80,  # 前年比（仮値）
                70,  # 平均値（仮値）
                90   # 最高値（仮値）
            ]
            values += values[:1]  # 円を閉じる

            # レーダーチャートの描画
            ax.plot(angles, values, 'o-', linewidth=3, color=self.colors['primary'], alpha=0.8)
            ax.fill(angles, values, alpha=0.25, color=self.colors['primary'])

            # 軸ラベルの設定
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(categories, fontsize=14, fontweight='bold')

            # 値の範囲設定
            ax.set_ylim(0, 100)
            ax.set_yticks([20, 40, 60, 80, 100])
            ax.set_yticklabels(['20', '40', '60', '80', '100'], fontsize=12)
            ax.grid(True)

            # 中央に現在値を表示
            ax.text(0, 0, '%s%s' % (val, unit), ha='center', va='center',
                   fontsize=24, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=60)

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "radar_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("レーダーチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("レーダーチャート作成エラー: %s", e)
            plt.close()
            return None

    def create_donut_chart(self, data, title="経済指標"):
        """
        ドーナツチャート（貿易・輸出入系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 10))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # ドーナツチャート用のデータ準備
            total = val * 1.3  # 全体を130%として設定
            remaining = total - val

            sizes = [val, remaining]
            labels = ['%s\n%s%s' % (indicator_name, val, unit), 'その他\n%.1f%s' % (remaining, unit)]
            colors = [self.colors['primary'], self.colors['secondary']]

            # ドーナツチャートの作成
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                             startangle=90, pctdistance=0.85,
                                             textprops={'fontsize': 14, 'fontweight': 'bold'})

            # 中央の穴を作成
            centre_circle = plt.Circle((0,0), 0.70, fc='white')
            fig.gca().add_artist(centre_circle)

            # 中央に値を表示
            ax.text(0, 0, '%s%s' % (val, unit), ha='center', va='center',
                   fontsize=32, fontweight='bold', color=self.colors['primary'])

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "donut_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("ドーナツチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("ドーナツチャート作成エラー: %s", e)
            plt.close()
            return None

    def create_area_chart(self, data, title="経済指標"):
        """
        エリアチャート（指数・建設系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(14, 8))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')
            historical_data = data.get('historical_data', [])

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # 時系列データがある場合は使用、ない場合は仮想データ生成
            if len(historical_data) >= 3:
                dates = []
                values = []
                unique_dates = set()

                for hist_item in historical_data[-12:]:  # 直近12ヶ月
                    date_value = hist_item.get('@time', '')
                    hist_value = hist_item.get('$')

                    if date_value and hist_value is not None:
                        try:
                            date_obj = None

                            # Pandas Timestampオブジェクトの場合
                            if hasattr(date_value, 'to_pydatetime'):
                                date_obj = date_value.to_pydatetime()
                            elif hasattr(date_value, 'strftime'):
                                date_obj = date_value
                            else:
                                # 文字列の場合、複数フォーマットを試行
                                date_str = str(date_value)
                                for fmt in ['%Y%m%d%H%M%S', '%Y%m%d', '%Y%m', '%Y', '%Y/%m/%d', '%Y-%m-%d']:
                                    try:
                                        date_obj = datetime.strptime(date_str, fmt)
                                        break
                                    except ValueError:
                                        continue

                            if date_obj:
                                # 年月日の組み合わせで一意性をチェック
                                date_key = date_obj.strftime('%Y-%m-%d')
                                unique_dates.add(date_key)
                                dates.append(date_obj)
                                values.append(float(hist_value))
                        except (ValueError, TypeError):
                            continue

                logger.info("エリアチャート用データ: %d件, 異なる日付: %d件", len(dates), len(unique_dates))

                # 真の時系列データかどうかを判定（異なる日付が3つ以上必要）
                if len(unique_dates) < 3:
                    logger.warning("時系列データではありません（異なる日付: %d件）- 地域別データまたは単一時点データの可能性", len(unique_dates))
                    logger.info("データ特性に応じた適切なチャートにフォールバック")
                    return self._create_fallback_chart(data, title)

                # 現在値を追加（最新の履歴データを使用）
                if dates and values:
                    # 最新の履歴データの値を使用（現在値ではなく）
                    pass  # 既に履歴データに含まれている
            else:
                # 仮想データ生成（過去6ヶ月）
                dates = []
                values = []
                base_date = datetime.now()

                for i in range(6, 0, -1):
                    month_ago = base_date.replace(month=base_date.month - i if base_date.month > i else base_date.month - i + 12,
                                                year=base_date.year - (1 if base_date.month <= i else 0))
                    dates.append(month_ago)
                    # 現在値の周辺でランダムな変動を生成
                    variation = val * (0.9 + 0.2 * np.random.random())
                    values.append(variation)

                dates.append(base_date)
                values.append(val)

            if not dates or not values:
                logger.warning("エリアチャート用のデータが不足: %s", indicator_name)
                return None

            # エリアチャートの作成
            ax.fill_between(dates, values, alpha=0.6, color=self.colors['primary'], label=indicator_name)
            ax.plot(dates, values, color=self.colors['primary'], linewidth=3, marker='o', markersize=6)

            # 最新値を強調
            if dates and values:
                latest_date, latest_value = dates[-1], values[-1]
                ax.scatter([latest_date], [latest_value], s=200, color=self.colors['accent'],
                          zorder=5, edgecolors='white', linewidth=2)

                # 最新値ラベル
                ax.annotate('%.1f%s' % (latest_value, unit),
                           (latest_date, latest_value),
                           textcoords="offset points", xytext=(10,10),
                           ha='left', fontsize=18, fontweight='bold',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)
            ax.set_ylabel('%s (%s)' % (indicator_name, unit), fontsize=12, fontweight='bold')
            ax.set_xlabel('期間', fontsize=12, fontweight='bold')

            # 日付軸の設定（短縮フォーマット）
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m'))
            plt.xticks(rotation=45, fontsize=10, ha='right')
            plt.yticks(fontsize=10)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--')

            # 凡例（コンパクトに）
            ax.legend(loc='upper left', fontsize=9, framealpha=0.9)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.15, left=0.12, right=0.95, top=0.9)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "area_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("エリアチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("エリアチャート作成エラー: %s", e)
            plt.close()
            return None

    def create_enhanced_bar_chart(self, data, title="経済指標"):
        """
        時系列データを活用した改良チャート（意味不明な比較項目棒グラフを廃止）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # 時系列データがあっても、シンプルな棒グラフを作成（時系列への自動変換を無効化）
            logger.info("シンプル棒グラフ作成: %s", data.get('name', 'Unknown'))

            # 時系列データが不足している場合はシンプルな現在値表示
            fig, ax = plt.subplots(figsize=(12, 8))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # シンプルな現在値表示（意味不明な比較項目は削除）
            categories = ['現在値']
            values = [val]

            # 色の設定
            colors = [self.colors['primary']]  # 現在値のみ

            # シンプルな棒グラフの作成
            bars = ax.bar(categories, values, color=colors, alpha=0.8,
                         edgecolor='white', linewidth=3)

            # 値をバーの上に表示
            for bar, val_item in zip(bars, values):
                height = bar.get_height()
                label = '%.1f%s' % (val_item, unit)
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                       label, ha='center', va='bottom',
                       fontweight='bold', fontsize=24,
                       bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=40)
            ax.set_ylabel('%s (%s)' % (indicator_name, unit), fontsize=22, fontweight='bold')
            ax.set_xlabel('', fontsize=22, fontweight='bold')  # X軸ラベルを削除

            plt.xticks(rotation=45, ha='right', fontsize=16)
            plt.yticks(fontsize=18)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')

            # 凡例
            ax.legend(loc='upper right', fontsize=14)

            # Y軸の範囲調整
            y_min = min(values) * 0.9
            y_max = max(values) * 1.1
            ax.set_ylim(y_min, y_max)

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "enhanced_bar_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("強化棒グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("強化棒グラフ作成エラー: %s", e)
            plt.close()
            return None

    def create_single_indicator_comparison(self, data, title="経済指標比較"):
        """
        単一指標の比較チャート（正常範囲との比較）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 8))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')
            normal_range = data.get('normal_range', [])

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            # 比較データの準備
            categories = ['現在値']
            values = [float(value)]
            colors = [self.colors['primary']]

            # 正常範囲がある場合は追加
            if normal_range and len(normal_range) >= 2:
                categories.extend(['正常範囲下限', '正常範囲上限'])
                values.extend([normal_range[0], normal_range[1]])
                colors.extend([self.colors['info'], self.colors['info']])

            # 棒グラフを作成
            bars = ax.bar(categories, values, color=colors, alpha=0.8, edgecolor='white', linewidth=2)

            # 値をバーの上に表示
            for bar, val in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                       '%.2f%s' % (val, unit), ha='center', va='bottom',
                       fontweight='bold', fontsize=16)

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=40)
            ax.set_ylabel('%s (%s)' % (indicator_name, unit), fontsize=22, fontweight='bold')

            plt.xticks(rotation=45, ha='right', fontsize=18)
            plt.yticks(fontsize=20)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "comparison_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("比較チャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("比較チャート作成エラー: %s", e)
            plt.close()
            return None

    def create_simple_bar_chart(self, data, title="経済指標"):
        """
        シンプルな棒グラフ（最後のフォールバック）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(10, 6))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            # シンプルな棒グラフ
            bar = ax.bar([indicator_name], [float(value)],
                        color=self.colors['primary'], alpha=0.8,
                        edgecolor='white', linewidth=2)

            # 値をバーの上に表示
            height = bar[0].get_height()
            ax.text(bar[0].get_x() + bar[0].get_width()/2., height + height*0.02,
                   '%s%s' % (value, unit), ha='center', va='bottom',
                   fontweight='bold', fontsize=20)

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)
            ax.set_ylabel('値 (%s)' % unit, fontsize=12, fontweight='bold')

            plt.xticks(fontsize=11)
            plt.yticks(fontsize=11)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.15, left=0.12, right=0.95, top=0.9)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "simple_bar_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("シンプル棒グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("シンプル棒グラフ作成エラー: %s", e)
            plt.close()
            return None

    def cleanup_old_charts(self, max_age_hours=24):
        """
        古いチャートファイルを削除

        Args:
            max_age_hours: 保持する最大時間（時間）
        """
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=max_age_hours)

            for filename in os.listdir(self.output_dir):
                filepath = os.path.join(self.output_dir, filename)
                if os.path.isfile(filepath):
                    file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                    if file_time < cutoff_time:
                        os.remove(filepath)
                        logger.info("古いチャートファイルを削除: %s", filename)

        except Exception as e:
            logger.error("チャートファイルクリーンアップエラー: %s", e)

    def create_line_with_markers_chart(self, data, title):
        """マーカー付き線グラフを作成"""
        try:
            fig, ax = plt.subplots(figsize=(16, 10))

            indicator_name = data.get('name', 'Unknown')
            unit = data.get('unit', '')
            historical_data = data.get('historical_data', [])

            dates = []
            values = []

            if historical_data:
                for hist_item in historical_data:
                    date_value = hist_item.get('@time', '')
                    value = hist_item.get('$')

                    if date_value and value is not None:
                        try:
                            date_obj = None

                            # Pandas Timestampオブジェクトの場合
                            if hasattr(date_value, 'to_pydatetime'):
                                date_obj = date_value.to_pydatetime()
                            elif hasattr(date_value, 'strftime'):
                                date_obj = date_value
                            else:
                                date_str = str(date_value)
                                for fmt in ['%Y%m%d', '%Y%m', '%Y', '%Y/%m/%d', '%Y-%m-%d']:
                                    try:
                                        date_obj = datetime.strptime(date_str, fmt)
                                        break
                                    except ValueError:
                                        continue

                            if date_obj:
                                dates.append(date_obj)
                                values.append(float(value))
                        except (ValueError, TypeError):
                            continue

            if not dates or len(dates) < 2:
                logger.warning("マーカー付き線グラフに十分なデータがありません")
                return self.create_enhanced_bar_chart(data, title)

            # データをソート
            sorted_data = sorted(zip(dates, values))
            dates, values = zip(*sorted_data)

            # マーカー付き線グラフを作成
            ax.plot(dates, values, marker='o', markersize=8, linewidth=3,
                   markerfacecolor='white', markeredgecolor='#2E86AB',
                   markeredgewidth=2, color='#2E86AB', alpha=0.8)

            # データポイントに値を表示
            for date, value in zip(dates, values):
                ax.annotate('{:.1f}'.format(value), (date, value),
                           textcoords="offset points", xytext=(0,10),
                           ha='center', fontsize=12, fontweight='bold')

            # チャートの装飾
            ax.set_title(title, fontsize=24, fontweight='bold', pad=20)
            ax.set_xlabel('時期', fontsize=16, fontweight='bold')
            ax.set_ylabel('{} ({})'.format(indicator_name, unit), fontsize=16, fontweight='bold')

            # グリッドを追加
            ax.grid(True, alpha=0.3, linestyle='--')

            # 日付軸の書式設定
            ax.tick_params(axis='x', rotation=45, labelsize=12)
            ax.tick_params(axis='y', labelsize=12)

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "line_markers_{}.png".format(timestamp)
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            logger.info("マーカー付き線グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("マーカー付き線グラフ作成エラー: %s", e)
            return None

    def create_horizontal_bar_chart(self, data, title="経済指標"):
        """
        横棒グラフを作成（地域別データや長いラベルに適している）

        Args:
            data: 統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(14, 10))

            indicator_name = data.get('name', 'Unknown')
            unit = data.get('unit', '')
            historical_data = data.get('historical_data', [])

            # 履歴データから地域別データを抽出
            regions = []
            values = []

            for item in historical_data[:15]:  # 上位15地域
                if isinstance(item, dict):
                    area_code = item.get('@area', '')
                    value = item.get('$')

                    if value is not None:
                        try:
                            val = float(value)
                            # 地域コードを地域名に変換（簡易版）
                            region_name = self._get_region_name(area_code)
                            regions.append(region_name)
                            values.append(val)
                        except (ValueError, TypeError):
                            continue

            if not regions or not values:
                logger.warning("横棒グラフ用データが不足")
                return self.create_simple_bar_chart(data, title)

            # 値でソート（降順）
            sorted_data = sorted(zip(regions, values), key=lambda x: x[1], reverse=True)
            regions, values = zip(*sorted_data)

            # 横棒グラフを作成
            bars = ax.barh(range(len(regions)), values,
                          color=plt.cm.viridis(np.linspace(0, 1, len(regions))),
                          alpha=0.8, edgecolor='white', linewidth=1)

            # 値をバーの右端に表示
            for i, (region, value) in enumerate(zip(regions, values)):
                ax.text(value + max(values) * 0.01, i, f'{value:,.0f}',
                       va='center', ha='left', fontsize=12, fontweight='bold')

            # 軸とラベルの設定
            ax.set_yticks(range(len(regions)))
            ax.set_yticklabels(regions, fontsize=11)
            ax.set_xlabel(f'{indicator_name} ({unit})', fontsize=14, fontweight='bold')
            ax.set_title(f'{indicator_name}の地域別分布', fontsize=18, fontweight='bold', pad=20)

            # グリッドとスタイル
            ax.grid(axis='x', alpha=0.3, linestyle='--')
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)

            plt.tight_layout()

            # ファイル保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"horizontal_bar_{timestamp}.png"
            chart_path = os.path.join(self.charts_dir, filename)
            plt.savefig(chart_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("横棒グラフ作成完了: %s", chart_path)
            return chart_path

        except Exception as e:
            logger.error("横棒グラフ作成エラー: %s", e)
            plt.close()
            return None

    def create_donut_chart(self, data, title="経済指標"):
        """
        ドーナツチャートを作成（カテゴリ別データに適している）

        Args:
            data: 統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 10))

            indicator_name = data.get('name', 'Unknown')
            unit = data.get('unit', '')
            historical_data = data.get('historical_data', [])

            # 履歴データからカテゴリ別データを抽出
            categories = {}

            for item in historical_data[:10]:  # 上位10カテゴリ
                if isinstance(item, dict):
                    # カテゴリコードを取得
                    cat_code = item.get('@cat01', item.get('@cat02', item.get('@cat03', '')))
                    value = item.get('$')

                    if value is not None and cat_code:
                        try:
                            val = float(value)
                            cat_name = self._get_category_name(cat_code)
                            if cat_name in categories:
                                categories[cat_name] += val
                            else:
                                categories[cat_name] = val
                        except (ValueError, TypeError):
                            continue

            if not categories:
                logger.warning("ドーナツチャート用データが不足")
                return self.create_simple_bar_chart(data, title)

            # データを準備
            labels = list(categories.keys())
            sizes = list(categories.values())
            colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))

            # ドーナツチャートを作成
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors,
                                             autopct='%1.1f%%', startangle=90,
                                             wedgeprops=dict(width=0.5, edgecolor='white'))

            # 中央に指標名を表示
            ax.text(0, 0, indicator_name, ha='center', va='center',
                   fontsize=14, fontweight='bold')

            # タイトル設定
            ax.set_title(f'{indicator_name}の構成比', fontsize=18, fontweight='bold', pad=20)

            plt.tight_layout()

            # ファイル保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"donut_{timestamp}.png"
            chart_path = os.path.join(self.charts_dir, filename)
            plt.savefig(chart_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("ドーナツチャート作成完了: %s", chart_path)
            return chart_path

        except Exception as e:
            logger.error("ドーナツチャート作成エラー: %s", e)
            plt.close()
            return None

    def _get_region_name(self, area_code):
        """地域コードを地域名に変換（簡易版）"""
        region_map = {
            '00000': '全国',
            '01000': '北海道',
            '02000': '青森県',
            '03000': '岩手県',
            '04000': '宮城県',
            '05000': '秋田県',
            '06000': '山形県',
            '07000': '福島県',
            '08000': '茨城県',
            '09000': '栃木県',
            '10000': '群馬県',
            '11000': '埼玉県',
            '12000': '千葉県',
            '13000': '東京都',
            '14000': '神奈川県',
            '15000': '新潟県',
            '16000': '富山県',
            '17000': '石川県',
            '18000': '福井県',
            '19000': '山梨県',
            '20000': '長野県',
            '21000': '岐阜県',
            '22000': '静岡県',
            '23000': '愛知県',
            '24000': '三重県',
            '25000': '滋賀県',
            '26000': '京都府',
            '27000': '大阪府',
            '28000': '兵庫県',
            '29000': '奈良県',
            '30000': '和歌山県',
            '31000': '鳥取県',
            '32000': '島根県',
            '33000': '岡山県',
            '34000': '広島県',
            '35000': '山口県',
            '36000': '徳島県',
            '37000': '香川県',
            '38000': '愛媛県',
            '39000': '高知県',
            '40000': '福岡県',
            '41000': '佐賀県',
            '42000': '長崎県',
            '43000': '熊本県',
            '44000': '大分県',
            '45000': '宮崎県',
            '46000': '鹿児島県',
            '47000': '沖縄県'
        }
        return region_map.get(area_code, f'地域{area_code}')

    def _get_category_name(self, cat_code):
        """カテゴリコードをカテゴリ名に変換（簡易版）"""
        if not cat_code:
            return 'その他'

        # 基本的なカテゴリマッピング
        if cat_code.startswith('01'):
            return '製造業'
        elif cat_code.startswith('02'):
            return 'サービス業'
        elif cat_code.startswith('03'):
            return '建設業'
        elif cat_code.startswith('04'):
            return '小売業'
        elif cat_code.startswith('05'):
            return '卸売業'
        else:
            return f'カテゴリ{cat_code}'

    def create_scatter_chart(self, data, title):
        """散布図を作成"""
        try:
            fig, ax = plt.subplots(figsize=(16, 10))

            indicator_name = data.get('name', 'Unknown')
            unit = data.get('unit', '')
            historical_data = data.get('historical_data', [])

            dates = []
            values = []

            if historical_data:
                for hist_item in historical_data:
                    date_value = hist_item.get('@time', '')
                    value = hist_item.get('$')

                    if date_value and value is not None:
                        try:
                            date_obj = None

                            # Pandas Timestampオブジェクトの場合
                            if hasattr(date_value, 'to_pydatetime'):
                                date_obj = date_value.to_pydatetime()
                            elif hasattr(date_value, 'strftime'):
                                date_obj = date_value
                            else:
                                date_str = str(date_value)
                                for fmt in ['%Y%m%d', '%Y%m', '%Y', '%Y/%m/%d', '%Y-%m-%d']:
                                    try:
                                        date_obj = datetime.strptime(date_str, fmt)
                                        break
                                    except ValueError:
                                        continue

                            if date_obj:
                                dates.append(date_obj)
                                values.append(float(value))
                        except (ValueError, TypeError):
                            continue

            if not dates or len(dates) < 3:
                logger.warning("散布図に十分なデータがありません")
                return self.create_enhanced_bar_chart(data, title)

            # 散布図を作成
            scatter = ax.scatter(dates, values, s=120, alpha=0.7,
                               c=range(len(values)), cmap='viridis',
                               edgecolors='white', linewidth=2)

            # データポイントに値を表示
            for date, value in zip(dates, values):
                ax.annotate('{:.1f}'.format(value), (date, value),
                           textcoords="offset points", xytext=(5,5),
                           ha='left', fontsize=11, fontweight='bold')

            # チャートの装飾
            ax.set_title(title, fontsize=24, fontweight='bold', pad=20)
            ax.set_xlabel('時期', fontsize=16, fontweight='bold')
            ax.set_ylabel('{} ({})'.format(indicator_name, unit), fontsize=16, fontweight='bold')

            # グリッドを追加
            ax.grid(True, alpha=0.3, linestyle='--')

            # 日付軸の書式設定
            ax.tick_params(axis='x', rotation=45, labelsize=12)
            ax.tick_params(axis='y', labelsize=12)

            # カラーバーを追加
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label('時系列順序', fontsize=12)

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "scatter_{}.png".format(timestamp)
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            logger.info("散布図作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("散布図作成エラー: %s", e)
            return None

    def create_multi_indicator_bar_chart(self, chart_data, title, chart_type='comparison'):
        """複数指標の棒グラフ比較チャート"""
        try:
            logger.info("複数指標棒グラフ作成開始: %d指標", len(chart_data))

            # フィギュアとサブプロットの設定
            fig, ax = plt.subplots(figsize=(12, 8))

            # データの準備
            names = [item['name'][:15] + '...' if len(item['name']) > 15 else item['name'] for item in chart_data]
            values = []
            units = []

            for item in chart_data:
                try:
                    value = float(item['value'])
                    values.append(value)
                    units.append(item.get('unit', ''))
                except (ValueError, TypeError):
                    values.append(0)
                    units.append('')

            # カラーパレットの設定
            colors = plt.cm.Set3(np.linspace(0, 1, len(chart_data)))

            # 棒グラフの作成
            bars = ax.bar(names, values, color=colors, alpha=0.8, edgecolor='black', linewidth=1)

            # 値ラベルの追加
            for bar, value, unit in zip(bars, values, units):
                height = bar.get_height()
                if height != 0:
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:,.1f}{unit}',
                           ha='center', va='bottom', fontsize=14, fontweight='bold')

            # タイトルとラベルの設定
            ax.set_title(title, fontsize=20, fontweight='bold', pad=20)
            ax.set_ylabel('値', fontsize=16, fontweight='bold')

            # X軸ラベルの回転
            plt.xticks(rotation=45, ha='right', fontsize=12)
            plt.yticks(fontsize=12)

            # グリッドの追加
            ax.grid(True, alpha=0.3, axis='y')
            ax.set_axisbelow(True)

            # レイアウトの調整
            plt.tight_layout()

            # ファイル保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"multi_bar_{timestamp}.png"
            filepath = os.path.join(self.charts_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            logger.info("複数指標棒グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("複数指標棒グラフ作成エラー: %s", e)
            return None

    def create_multi_line_chart(self, indicators, title):
        """複数指標の線グラフ比較チャート"""
        try:
            logger.info("複数指標線グラフ作成開始: %d指標", len(indicators))

            # フィギュアとサブプロットの設定
            fig, ax = plt.subplots(figsize=(14, 8))

            # カラーパレットの設定
            colors = plt.cm.tab10(np.linspace(0, 1, len(indicators)))

            for i, indicator in enumerate(indicators):
                time_series = indicator.get('time_series_data', [])
                if not time_series:
                    continue

                # 時系列データの準備
                dates = []
                values = []

                for point in time_series:
                    try:
                        date_str = point.get('date')
                        value = float(point.get('value', 0))

                        if date_str and value != 0:
                            # 日付の解析
                            if isinstance(date_str, str):
                                date = pd.to_datetime(date_str)
                            else:
                                date = date_str

                            dates.append(date)
                            values.append(value)
                    except (ValueError, TypeError):
                        continue

                if dates and values:
                    # 線グラフの描画
                    ax.plot(dates, values,
                           color=colors[i],
                           linewidth=3,
                           marker='o',
                           markersize=6,
                           label=indicator['name'][:20] + '...' if len(indicator['name']) > 20 else indicator['name'],
                           alpha=0.8)

            # タイトルとラベルの設定
            ax.set_title(title, fontsize=20, fontweight='bold', pad=20)
            ax.set_xlabel('期間', fontsize=16, fontweight='bold')
            ax.set_ylabel('値', fontsize=16, fontweight='bold')

            # 凡例の設定
            ax.legend(loc='best', fontsize=12, frameon=True, fancybox=True, shadow=True)

            # グリッドの追加
            ax.grid(True, alpha=0.3)
            ax.set_axisbelow(True)

            # 日付軸の書式設定
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
            plt.xticks(rotation=45)

            # レイアウトの調整
            plt.tight_layout()

            # ファイル保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"multi_line_{timestamp}.png"
            filepath = os.path.join(self.charts_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            logger.info("複数指標線グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("複数指標線グラフ作成エラー: %s", e)
            return None

    def create_normalized_comparison_chart(self, indicators, title):
        """正規化比較チャート（異なる単位の指標を比較）"""
        try:
            logger.info("正規化比較チャート作成開始: %d指標", len(indicators))

            # フィギュアとサブプロットの設定
            fig, ax = plt.subplots(figsize=(12, 8))

            # データの正規化（0-100スケール）
            names = []
            normalized_values = []
            original_values = []
            units = []

            for indicator in indicators:
                try:
                    value = float(indicator['value'])
                    names.append(indicator['name'][:15] + '...' if len(indicator['name']) > 15 else indicator['name'])
                    original_values.append(value)
                    units.append(indicator.get('unit', ''))
                except (ValueError, TypeError):
                    continue

            if original_values:
                # 正規化（最大値を100とする）
                max_value = max(original_values)
                normalized_values = [(val / max_value) * 100 for val in original_values]

                # カラーパレットの設定
                colors = plt.cm.viridis(np.linspace(0, 1, len(names)))

                # 棒グラフの作成
                bars = ax.bar(names, normalized_values, color=colors, alpha=0.8, edgecolor='black', linewidth=1)

                # 値ラベルの追加（元の値と正規化値）
                for bar, orig_val, norm_val, unit in zip(bars, original_values, normalized_values, units):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{orig_val:,.1f}{unit}\n({norm_val:.1f}%)',
                           ha='center', va='bottom', fontsize=12, fontweight='bold')

                # タイトルとラベルの設定
                ax.set_title(title, fontsize=20, fontweight='bold', pad=20)
                ax.set_ylabel('正規化値 (%)', fontsize=16, fontweight='bold')

                # X軸ラベルの回転
                plt.xticks(rotation=45, ha='right', fontsize=12)
                plt.yticks(fontsize=12)

                # グリッドの追加
                ax.grid(True, alpha=0.3, axis='y')
                ax.set_axisbelow(True)

                # レイアウトの調整
                plt.tight_layout()

                # ファイル保存
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"normalized_{timestamp}.png"
                filepath = os.path.join(self.charts_dir, filename)
                plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
                plt.close()

                logger.info("正規化比較チャート作成完了: %s", filepath)
                return filepath

        except Exception as e:
            logger.error("正規化比較チャート作成エラー: %s", e)
            return None

    def create_category_breakdown_chart(self, category_groups, title):
        """カテゴリ別分解チャート"""
        try:
            logger.info("カテゴリ分解チャート作成開始: %d カテゴリ", len(category_groups))

            # フィギュアとサブプロットの設定
            fig, ax = plt.subplots(figsize=(14, 8))

            # カテゴリごとのデータ準備
            categories = list(category_groups.keys())
            category_data = {}

            for category, indicators in category_groups.items():
                values = []
                names = []
                for indicator in indicators:
                    try:
                        value = float(indicator['value'])
                        values.append(value)
                        names.append(indicator['name'][:10] + '...' if len(indicator['name']) > 10 else indicator['name'])
                    except (ValueError, TypeError):
                        continue
                category_data[category] = {'values': values, 'names': names}

            # グループ化された棒グラフの作成
            x = np.arange(len(categories))
            width = 0.8 / max(len(data['values']) for data in category_data.values())

            colors = plt.cm.Set2(np.linspace(0, 1, max(len(data['values']) for data in category_data.values())))

            for i, (category, data) in enumerate(category_data.items()):
                for j, (value, name) in enumerate(zip(data['values'], data['names'])):
                    ax.bar(x[i] + j * width, value, width,
                          label=f"{category}: {name}" if i == 0 else "",
                          color=colors[j], alpha=0.8, edgecolor='black', linewidth=1)

            # タイトルとラベルの設定
            ax.set_title(title, fontsize=20, fontweight='bold', pad=20)
            ax.set_xlabel('カテゴリ', fontsize=16, fontweight='bold')
            ax.set_ylabel('値', fontsize=16, fontweight='bold')
            ax.set_xticks(x)
            ax.set_xticklabels(categories, fontsize=12)

            # 凡例の設定
            ax.legend(loc='upper left', bbox_to_anchor=(1, 1), fontsize=10)

            # グリッドの追加
            ax.grid(True, alpha=0.3, axis='y')
            ax.set_axisbelow(True)

            # レイアウトの調整
            plt.tight_layout()

            # ファイル保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"category_{timestamp}.png"
            filepath = os.path.join(self.charts_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            logger.info("カテゴリ分解チャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("カテゴリ分解チャート作成エラー: %s", e)
            return None
