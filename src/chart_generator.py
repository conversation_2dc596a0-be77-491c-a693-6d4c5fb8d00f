#!/usr/bin/env python3
"""
統計データ可視化モジュール
e-Stat統計データから美しいグラフ・チャートを生成
グラフタイプの自動選択と多様化機能付き
"""

import os
import logging
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.font_manager as fm
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
# from typing import List, Dict, Optional, Tuple

# 日本語フォント設定の強化
def setup_japanese_fonts():
    """日本語フォントの設定を強化"""
    try:
        # japanize_matplotlibを試す
        import japanize_matplotlib
        logger.info("japanize_matplotlib を使用して日本語フォントを設定")
        return True
    except ImportError:
        pass

    # システムの日本語フォントを検索
    japanese_fonts = [
        'IPAexGothic', 'IPAPGothic', 'IPAGothic', 'IPAexMincho', 'IPAMincho',
        'Hiragino Sans', 'Hiragino Kaku Gothic Pro', 'Yu Gothic', 'Meiryo',
        'MS Gothic', 'MS Mincho', 'Takao Gothic', 'VL PGothic', 'Noto Sans CJK JP'
    ]

    available_fonts = [f.name for f in fm.fontManager.ttflist]

    for font in japanese_fonts:
        if font in available_fonts:
            plt.rcParams['font.family'] = font
            logger.info("日本語フォントを設定: %s", font)
            return True

    # フォールバック: DejaVu Sans（英数字のみ）
    plt.rcParams['font.family'] = 'DejaVu Sans'
    logger.warning("日本語フォントが見つかりません。DejaVu Sansを使用")
    return False

# ログ設定
logger = logging.getLogger(__name__)

class ChartGenerator:
    """統計データ可視化クラス"""
    
    def __init__(self):
        """初期化"""
        # 日本語フォント設定の強化
        self.japanese_font_available = setup_japanese_fonts()

        # スタイル設定
        try:
            plt.style.use('seaborn-v0_8')
        except OSError:
            try:
                plt.style.use('seaborn')
            except OSError:
                plt.style.use('default')
        sns.set_palette("husl")

        # 日本語フォント設定の詳細
        if self.japanese_font_available:
            plt.rcParams['font.family'] = ['IPAexGothic', 'IPAPGothic', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', 'Takao', 'VL PGothic', 'Noto Sans CJK JP']
            plt.rcParams['font.sans-serif'] = ['IPAexGothic', 'IPAPGothic', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', 'Takao', 'VL PGothic', 'Noto Sans CJK JP']
        else:
            plt.rcParams['font.family'] = ['DejaVu Sans']
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

        plt.rcParams['axes.unicode_minus'] = False  # マイナス記号の文字化け防止
        plt.rcParams['figure.figsize'] = (12, 8)  # 適切なサイズに調整
        plt.rcParams['font.size'] = 12  # 基本フォントサイズ
        plt.rcParams['axes.grid'] = True
        plt.rcParams['grid.alpha'] = 0.3

        # 各要素のフォントサイズ設定（バランス重視）
        plt.rcParams['axes.titlesize'] = 24      # タイトル（適切なサイズ）
        plt.rcParams['axes.labelsize'] = 16      # 軸ラベル
        plt.rcParams['xtick.labelsize'] = 14     # X軸目盛りラベル
        plt.rcParams['ytick.labelsize'] = 14     # Y軸目盛りラベル
        plt.rcParams['legend.fontsize'] = 12     # 凡例
        plt.rcParams['figure.titlesize'] = 28    # 図全体のタイトル

        # 出力ディレクトリ
        self.output_dir = "charts"
        os.makedirs(self.output_dir, exist_ok=True)

        # カラーパレット
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#F18F01',
            'success': '#C73E1D',
            'info': '#5D737E',
            'light': '#F5F5F5',
            'dark': '#2C3E50',
            'warning': '#FF9500',
            'danger': '#FF3B30'
        }

        # グラフタイプ選択の履歴管理
        self.chart_type_history = []
    
    def create_time_series_chart(self, data_list, title="経済指標推移"):
        """
        時系列チャートを作成
        
        Args:
            data_list: 統計データのリスト
            title: チャートタイトル
            
        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(16, 10))
            
            # データを指標別にグループ化
            indicator_data = {}
            for data in data_list:
                indicator_name = data.get('name', 'Unknown')
                if indicator_name not in indicator_data:
                    indicator_data[indicator_name] = {
                        'dates': [],
                        'values': [],
                        'unit': data.get('unit', ''),
                        'category': data.get('category', '')
                    }

                # 履歴データから時系列を構築
                historical_data = data.get('historical_data', [])
                if historical_data:
                    for hist_item in historical_data:
                        date_str = hist_item.get('@time', '')
                        value = hist_item.get('$')

                        if date_str and value is not None:
                            try:
                                # 複数の日付フォーマットを試す
                                date_obj = None
                                for fmt in ['%Y%m%d', '%Y%m', '%Y', '%Y/%m/%d', '%Y-%m-%d']:
                                    try:
                                        date_obj = datetime.strptime(str(date_str), fmt)
                                        break
                                    except ValueError:
                                        continue

                                if date_obj:
                                    indicator_data[indicator_name]['dates'].append(date_obj)
                                    indicator_data[indicator_name]['values'].append(float(value))
                            except (ValueError, TypeError):
                                continue
                else:
                    # 履歴データがない場合は現在の値のみ使用
                    date_str = data.get('time', '')
                    value = data.get('value')

                    if value is not None:
                        try:
                            # 現在の日付を使用
                            date_obj = datetime.now()
                            indicator_data[indicator_name]['dates'].append(date_obj)
                            indicator_data[indicator_name]['values'].append(float(value))
                        except (ValueError, TypeError):
                            continue
            
            # 各指標をプロット
            color_idx = 0
            colors_list = list(self.colors.values())

            for indicator_name, data_info in indicator_data.items():
                if len(data_info['dates']) > 0:
                    color = colors_list[color_idx % len(colors_list)]

                    # データをソート
                    sorted_data = sorted(zip(data_info['dates'], data_info['values']))
                    dates, values = zip(*sorted_data)

                    # プロット（線を確実に表示）
                    if len(dates) >= 2:
                        # 複数データポイントがある場合は必ず線グラフ
                        logger.info("複数指標折れ線グラフ描画: %s (%d点)", indicator_name, len(dates))

                        line = ax.plot(dates, values,
                                      linestyle='-',        # 実線を明示
                                      marker='o',           # 円形マーカー
                                      linewidth=3,          # 太い線
                                      markersize=6,         # 適度なマーカー
                                      color=color,
                                      markerfacecolor='white',
                                      markeredgecolor=color,
                                      markeredgewidth=2,
                                      label="%s (%s)" % (indicator_name, data_info['unit']),
                                      alpha=1.0,            # 完全不透明
                                      zorder=10)            # 最前面に表示

                        if line:
                            logger.info("複数指標線グラフ作成成功: %s", indicator_name)
                        else:
                            logger.error("複数指標線グラフ作成失敗: %s", indicator_name)

                    elif len(dates) == 1:
                        # 単一データポイントの場合はマーカーのみ
                        ax.scatter(dates, values,
                                  s=100,
                                  color=color,
                                  alpha=1.0,
                                  edgecolors='white',
                                  linewidth=2,
                                  label="%s (%s)" % (indicator_name, data_info['unit']),
                                  zorder=10)
                        logger.info("複数指標単一ポイント表示: %s", indicator_name)
                    else:
                        logger.warning("複数指標でデータポイントが0個: %s", indicator_name)

                    color_idx += 1

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=20, fontweight='bold', pad=20)
            ax.set_xlabel('日付', fontsize=14, fontweight='bold')
            ax.set_ylabel('値', fontsize=14, fontweight='bold')

            # 日付軸の設定（短縮フォーマット）
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.xticks(rotation=45, fontsize=12, ha='right')
            plt.yticks(fontsize=12)

            # 凡例（画面内に配置）
            ax.legend(loc='upper left', fontsize=10, framealpha=0.9)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--')

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.15, left=0.1, right=0.95, top=0.9)
            
            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "timeseries_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)
            
            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info("時系列チャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("時系列チャート作成エラー: %s", e)
            plt.close()
            return None
    
    def create_comparison_chart(self, data_list, title="経済指標比較"):
        """
        比較バーチャートを作成
        
        Args:
            data_list: 統計データのリスト
            title: チャートタイトル
            
        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # データの準備
            indicators = []
            values = []
            colors_list = []
            
            for i, data in enumerate(data_list[:8]):  # 最大8個まで
                name = data.get('name', 'Unknown')
                value = data.get('value')
                unit = data.get('unit', '')
                
                if value is not None:
                    indicators.append("{name}\n(%s)" % (unit,))
                    values.append(value)
                    colors_list.append(list(self.colors.values())[i % len(self.colors)])
            
            if not values:
                logger.warning("比較チャート用のデータがありません")
                return None
            
            # チャート作成
            fig, ax = plt.subplots(figsize=(16, 10))

            bars = ax.bar(indicators, values, color=colors_list, alpha=0.8, edgecolor='white', linewidth=2)

            # 値をバーの上に表示（大きなフォント）
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                       '%.2f' % value, ha='center', va='bottom', fontweight='bold', fontsize=16)

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=18, fontweight='bold', pad=15)
            ax.set_ylabel('値', fontsize=14, fontweight='bold')

            # X軸ラベルの回転と適切なフォント
            plt.xticks(rotation=45, ha='right', fontsize=11)
            plt.yticks(fontsize=11)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.2, left=0.12, right=0.95, top=0.9)
            
            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "comparison_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)
            
            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info("比較チャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("比較チャート作成エラー: %s", e)
            plt.close()
            return None
    
    def create_trend_analysis_chart(self, data_list, title="トレンド分析"):
        """
        トレンド分析チャートを作成（変化率を可視化）
        
        Args:
            data_list: 統計データのリスト
            title: チャートタイトル
            
        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # 変化率データの準備
            indicators = []
            change_rates = []
            colors_list = []

            for data in data_list:
                time_series_analysis = data.get('time_series_analysis', {})
                change_rate = time_series_analysis.get('change_rate')

                if change_rate is not None:
                    name = data.get('name', 'Unknown')
                    # 名前を短縮
                    if len(name) > 20:
                        name = name[:17] + "..."
                    indicators.append(name)
                    change_rates.append(change_rate)

                    # 色を変化率に応じて設定
                    if change_rate > 0:
                        colors_list.append(self.colors['success'])  # 上昇は赤
                    elif change_rate < 0:
                        colors_list.append(self.colors['primary'])  # 下降は青
                    else:
                        colors_list.append(self.colors['info'])     # 変化なしはグレー
            
            if not change_rates:
                logger.warning("トレンド分析チャート用のデータがありません")
                return None
            
            # チャート作成
            fig, ax = plt.subplots(figsize=(16, 10))

            bars = ax.barh(indicators, change_rates, color=colors_list, alpha=0.8, edgecolor='white', linewidth=2)

            # 0ラインを強調
            ax.axvline(x=0, color='black', linewidth=2, alpha=0.8)

            # 値をバーの端に表示（大きなフォント）
            for bar, rate in zip(bars, change_rates):
                width = bar.get_width()
                x_pos = width + (0.2 if width >= 0 else -0.2)
                ax.text(x_pos, bar.get_y() + bar.get_height()/2.,
                       '%+.1f%%' % rate, ha='left' if width >= 0 else 'right',
                       va='center', fontweight='bold', fontsize=16)

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=40)
            ax.set_xlabel('変化率 (%)', fontsize=22, fontweight='bold')

            # Y軸ラベルのフォントサイズ
            plt.yticks(fontsize=18)
            plt.xticks(fontsize=20)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='x')
            
            # レイアウト調整
            plt.tight_layout()
            
            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "trend_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)
            
            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info("トレンド分析チャート作成完了: %s", filepath)
            return filepath
            
        except Exception as e:
            logger.error("トレンド分析チャート作成エラー: %s", e)
            plt.close()
            return None
    
    def create_comprehensive_chart(self, data_list, title="経済指標総合分析"):
        """
        包括的な分析チャートを作成（複数のサブプロットを含む）
        
        Args:
            data_list: 統計データのリスト
            title: チャートタイトル
            
        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig = plt.figure(figsize=(20, 14))

            # サブプロット1: 最新値比較
            ax1 = plt.subplot(2, 2, 1)
            indicators = [data.get('name', 'Unknown')[:12] + '...' if len(data.get('name', '')) > 12
                         else data.get('name', 'Unknown') for data in data_list[:6]]
            values = [data.get('value', 0) for data in data_list[:6]]
            colors = [list(self.colors.values())[i % len(self.colors)] for i in range(len(values))]

            bars1 = ax1.bar(indicators, values, color=colors, alpha=0.8)
            ax1.set_title('最新値比較', fontweight='bold', fontsize=36)
            ax1.tick_params(axis='x', rotation=45, labelsize=16)
            ax1.tick_params(axis='y', labelsize=16)

            # 値をバーの上に表示
            for bar, value in zip(bars1, values):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                        '%.1f' % value, ha='center', va='bottom', fontweight='bold', fontsize=11)
            
            # サブプロット2: 変化率
            ax2 = plt.subplot(2, 2, 2)
            change_data = [(data.get('name', 'Unknown')[:12] + '...' if len(data.get('name', '')) > 12
                           else data.get('name', 'Unknown'),
                           data.get('trend_analysis', {}).get('change_rate', 0))
                          for data in data_list if data.get('trend_analysis', {}).get('change_rate') is not None]

            if change_data:
                names, rates = zip(*change_data[:6])
                colors2 = ['red' if r > 0 else 'blue' if r < 0 else 'gray' for r in rates]
                bars2 = ax2.barh(names, rates, color=colors2, alpha=0.8)
                ax2.set_title('変化率 (%)', fontweight='bold', fontsize=36)
                ax2.axvline(x=0, color='black', linewidth=1)
                ax2.tick_params(axis='both', labelsize=16)

                # 値をバーの端に表示
                for bar, rate in zip(bars2, rates):
                    width = bar.get_width()
                    x_pos = width + (0.1 if width >= 0 else -0.1)
                    ax2.text(x_pos, bar.get_y() + bar.get_height()/2.,
                            '%+.1f%%' % rate, ha='left' if width >= 0 else 'right',
                            va='center', fontweight='bold', fontsize=11)
            
            # サブプロット3: カテゴリ別分布
            ax3 = plt.subplot(2, 2, 3)
            categories = {}
            for data in data_list:
                cat = data.get('category', 'その他')
                categories[cat] = categories.get(cat, 0) + 1

            if categories:
                wedges, texts, autotexts = ax3.pie(categories.values(), labels=categories.keys(),
                                                  autopct='%1.1f%%', startangle=90, textprops={'fontsize': 12})
                ax3.set_title('カテゴリ別分布', fontweight='bold', fontsize=36)
                # パーセンテージのフォントサイズを調整
                for autotext in autotexts:
                    autotext.set_fontsize(12)
                    autotext.set_fontweight('bold')

            # サブプロット4: 重要度別
            ax4 = plt.subplot(2, 2, 4)
            importance_data = {}
            for data in data_list:
                imp = data.get('importance', 'medium')
                importance_data[imp] = importance_data.get(imp, 0) + 1

            if importance_data:
                imp_colors = {'high': 'red', 'medium': 'orange', 'low': 'green'}
                colors4 = [imp_colors.get(imp, 'gray') for imp in importance_data.keys()]
                bars4 = ax4.bar(importance_data.keys(), importance_data.values(), color=colors4, alpha=0.8)
                ax4.set_title('重要度別分布', fontweight='bold', fontsize=36)
                ax4.tick_params(axis='both', labelsize=16)

                # 値をバーの上に表示
                for bar, value in zip(bars4, importance_data.values()):
                    height = bar.get_height()
                    ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                            '%s' % value, ha='center', va='bottom', fontweight='bold', fontsize=12)

            # 全体タイトル
            fig.suptitle(title, fontsize=52, fontweight='bold', y=0.98)
            
            # レイアウト調整
            plt.tight_layout()
            plt.subplots_adjust(top=0.93)
            
            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "comprehensive_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)
            
            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()
            
            logger.info("包括的分析チャート作成完了: %s", filepath)
            return filepath
            
        except Exception as e:
            logger.error("包括的分析チャート作成エラー: %s", e)
            plt.close()
            return None
    
    def create_pie_chart(self, data_list, title="経済指標構成比"):
        """
        円グラフを作成（構成比や割合データに適用）

        Args:
            data_list: 統計データのリスト
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # データの準備
            labels = []
            values = []
            colors_list = []

            for i, data in enumerate(data_list[:8]):  # 最大8個まで
                name = data.get('name', 'Unknown')
                value = data.get('value')

                if value is not None and value > 0:  # 正の値のみ
                    # 名前を短縮
                    if len(name) > 12:
                        name = name[:10] + "..."
                    labels.append(name)
                    values.append(abs(value))  # 絶対値を使用
                    colors_list.append(list(self.colors.values())[i % len(self.colors)])

            if not values:
                logger.warning("円グラフ用のデータがありません")
                return None

            # チャート作成
            fig, ax = plt.subplots(figsize=(14, 10))

            wedges, texts, autotexts = ax.pie(values, labels=labels, colors=colors_list,
                                            autopct='%1.1f%%', startangle=90,
                                            textprops={'fontsize': 14})

            # パーセンテージのフォントサイズを調整
            for autotext in autotexts:
                autotext.set_fontsize(16)
                autotext.set_fontweight('bold')
                autotext.set_color('white')

            # ラベルのフォントサイズを調整
            for text in texts:
                text.set_fontsize(16)
                text.set_fontweight('bold')

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)

            # 凡例を追加（画面内に配置）
            ax.legend(wedges, ["%s: %.1f" % (label, value) for label, value in zip(labels, values)],
                     title="指標値", loc="upper left", bbox_to_anchor=(1.05, 1),
                     fontsize=10)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(left=0.1, right=0.75, top=0.9, bottom=0.1)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "pie_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("円グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("円グラフ作成エラー: %s", e)
            plt.close()
            return None

    def create_line_chart(self, data_list, title="経済指標推移"):
        """
        折れ線グラフを作成（時系列データや推移に適用）

        Args:
            data_list: 統計データのリスト
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(16, 10))

            # データの準備
            indicators = []
            values = []
            colors_list = []

            for i, data in enumerate(data_list):
                name = data.get('name', 'Unknown')
                value = data.get('value')

                if value is not None:
                    # 名前を短縮
                    if len(name) > 15:
                        name = name[:12] + "..."
                    indicators.append(name)
                    values.append(value)
                    colors_list.append(list(self.colors.values())[i % len(self.colors)])

            if not values:
                logger.warning("折れ線グラフ用のデータがありません")
                return None

            # 折れ線グラフを作成
            x_positions = range(len(indicators))
            ax.plot(x_positions, values, marker='o', linewidth=4, markersize=10,
                   color=self.colors['primary'], alpha=0.8)

            # データポイントに値を表示
            for i, (x, y) in enumerate(zip(x_positions, values)):
                ax.annotate('%.1f' % y, (x, y), textcoords="offset points",
                           xytext=(0,15), ha='center', fontsize=16, fontweight='bold')

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=40)
            ax.set_xlabel('指標', fontsize=22, fontweight='bold')
            ax.set_ylabel('値', fontsize=22, fontweight='bold')

            # X軸の設定
            ax.set_xticks(x_positions)
            ax.set_xticklabels(indicators, rotation=45, ha='right', fontsize=18)
            plt.yticks(fontsize=20)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--')

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "line_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("折れ線グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("折れ線グラフ作成エラー: %s", e)
            plt.close()
            return None

    def determine_optimal_chart_type(self, data_list, single_indicator=False):
        """
        データの特性に基づいて最適なグラフタイプを自動判断

        Args:
            data_list: 統計データのリスト
            single_indicator: 単一指標かどうか

        Returns:
            dict: 推奨グラフタイプと理由
        """
        try:
            if not data_list:
                return {'type': 'simple_bar', 'reason': 'データが空のため、シンプルな棒グラフを選択'}

            # 単一指標の場合
            if single_indicator or len(data_list) == 1:
                return self._determine_single_indicator_chart_type(data_list[0] if data_list else {})

            # 複数指標の場合
            return self._determine_multiple_indicators_chart_type(data_list)

        except Exception as e:
            logger.error("グラフタイプ決定エラー: %s", e)
            return {'type': 'simple_bar', 'reason': 'エラーのため、フォールバック棒グラフを選択'}

    def _determine_single_indicator_chart_type(self, data):
        """単一指標のグラフタイプを決定"""
        try:
            indicator_name = data.get('name', '').lower()
            unit = str(data.get('unit', '')).lower()
            value = data.get('value')
            historical_data = data.get('historical_data', [])

            # 時系列データの詳細ログ出力
            logger.info("=== チャートタイプ決定 ===")
            logger.info("指標名: %s", indicator_name)
            logger.info("現在値: %s %s", value, unit)
            logger.info("時系列データ件数: %d件", len(historical_data))
            if len(historical_data) > 0:
                logger.info("時系列データサンプル: %s", historical_data[:3])

            # 時系列データの有無と品質をチェック
            has_sufficient_time_series = len(historical_data) >= 5

            # 指標の特性分析
            is_rate = '率' in indicator_name or '%' in unit or 'rate' in indicator_name
            is_index = '指数' in indicator_name or 'index' in indicator_name
            is_price = '物価' in indicator_name or '価格' in indicator_name or 'price' in indicator_name
            is_production = '生産' in indicator_name or '製造' in indicator_name or 'production' in indicator_name
            is_trade = '輸出' in indicator_name or '輸入' in indicator_name or '貿易' in indicator_name
            is_construction = '建設' in indicator_name or '住宅' in indicator_name or 'construction' in indicator_name
            is_employment = '雇用' in indicator_name or '失業' in indicator_name or 'employment' in indicator_name

            # 時系列データが十分にある場合（5点以上）は時系列チャートを優先
            if has_sufficient_time_series:
                return {'type': 'time_series', 'reason': f'時系列データが豊富（{len(historical_data)}件）なため、線グラフで時系列チャートが最適'}

            # 中程度の時系列データ（2-4点）は指標特性に応じて多様化
            elif len(historical_data) >= 2:
                import random

                # 物価・指数系は時系列を優先
                if is_price or is_index:
                    chart_types = ['time_series', 'area', 'gauge']
                    selected = random.choice(chart_types)
                    return {'type': selected, 'reason': f'物価・指数系指標で{selected}を選択（データ: {len(historical_data)}件）'}

                # 雇用系は多様なチャートタイプ
                elif is_employment:
                    chart_types = ['gauge', 'donut', 'time_series']
                    selected = random.choice(chart_types)
                    return {'type': selected, 'reason': f'雇用系指標で{selected}を選択（データ: {len(historical_data)}件）'}

                # 生産系はエリアチャートやゲージ
                elif is_production:
                    chart_types = ['area', 'gauge', 'enhanced_bar']
                    selected = random.choice(chart_types)
                    return {'type': selected, 'reason': f'生産系指標で{selected}を選択（データ: {len(historical_data)}件）'}

                # その他は多様なチャートタイプ
                else:
                    chart_types = ['time_series', 'gauge', 'area', 'enhanced_bar']
                    selected = random.choice(chart_types)
                    return {'type': selected, 'reason': f'一般指標で{selected}を選択（データ: {len(historical_data)}件）'}

            # 単一データポイントは視覚的インパクトのあるチャート
            elif len(historical_data) >= 1:
                import random
                chart_types = ['gauge', 'donut', 'enhanced_bar']
                selected = random.choice(chart_types)
                return {'type': selected, 'reason': f'単一データで{selected}を選択（データ: {len(historical_data)}件）'}

            # 時系列データが不足している場合の多様なチャートタイプ選択
            logger.info("時系列データ不足 - 多様なチャートタイプから選択: %s", indicator_name)
            import random

            # 指標特性に応じた多様なチャートタイプ選択
            if is_rate:
                # 率系指標は視覚的インパクトのあるチャート
                chart_types = ['gauge', 'donut', 'time_series']
                selected = random.choice(chart_types)
                return {'type': selected, 'reason': f'率系指標で{selected}チャートを選択 (指標: {indicator_name})'}
            elif is_trade:
                # 貿易系指標は棒グラフやドーナツ
                chart_types = ['enhanced_bar', 'donut', 'area']
                selected = random.choice(chart_types)
                return {'type': selected, 'reason': f'貿易系指標で{selected}チャートを選択'}
            elif is_construction:
                # 建設系指標は棒グラフやエリア
                chart_types = ['enhanced_bar', 'area', 'gauge']
                selected = random.choice(chart_types)
                return {'type': selected, 'reason': f'建設系指標で{selected}チャートを選択'}
            elif is_index or is_price:
                # 指数・物価系指標は多様なチャート
                chart_types = ['gauge', 'area', 'time_series']
                selected = random.choice(chart_types)
                return {'type': selected, 'reason': f'指数・物価系指標で{selected}チャートを選択'}
            elif is_production:
                # 生産系指標はエリアや棒グラフ
                chart_types = ['area', 'enhanced_bar', 'gauge']
                selected = random.choice(chart_types)
                return {'type': selected, 'reason': f'生産系指標で{selected}チャートを選択'}

            # デフォルト: 多様なチャートタイプから選択
            chart_types = ['gauge', 'donut', 'enhanced_bar', 'area', 'time_series']
            selected = random.choice(chart_types)
            return {'type': selected, 'reason': f'デフォルトで{selected}チャートを選択'}

        except Exception as e:
            logger.error("単一指標グラフタイプ決定エラー: %s", e)
            return {'type': 'simple_bar', 'reason': 'エラーのため、フォールバック棒グラフを選択'}

    def _determine_multiple_indicators_chart_type(self, data_list):
        """複数指標のグラフタイプを決定"""
        try:
            data_count = len(data_list)

            # 指標の特性分析
            indicator_names = [str(data.get('name', '')).lower() for data in data_list]
            combined_names = ' '.join(indicator_names)

            has_rates = sum('率' in name for name in indicator_names)
            has_indices = sum('指数' in name for name in indicator_names)
            has_amounts = sum('額' in name or '円' in str(data.get('unit', '')) for name, data in zip(indicator_names, data_list))

            # 変化率データの有無
            has_trend_data = sum(1 for data in data_list if data.get('time_series_analysis', {}).get('change_rate') is not None)

            # 時系列データの品質
            time_series_quality = sum(1 for data in data_list if len(data.get('historical_data', [])) >= 3)

            # 複数指標の判定ロジック
            if has_trend_data >= data_count * 0.7:  # 70%以上に変化率データがある
                return {'type': 'trend', 'reason': '複数指標で変化率データが豊富なため、トレンド分析チャートが最適'}
            elif time_series_quality >= data_count * 0.6:  # 60%以上に時系列データがある
                return {'type': 'time_series', 'reason': '複数指標で時系列データが豊富なため、時系列比較チャートが最適'}
            elif data_count >= 6:
                return {'type': 'comprehensive', 'reason': '指標数が多いため、包括的分析チャートで多面的表示'}
            elif has_rates >= data_count * 0.8:  # 80%以上が率系
                return {'type': 'comparison', 'reason': '率系指標が多いため、比較棒グラフが最適'}
            elif has_amounts >= data_count * 0.6:  # 60%以上が金額系
                if data_count <= 5:
                    return {'type': 'pie', 'reason': '金額系指標で少数のため、円グラフで構成比を表示'}
                else:
                    return {'type': 'comparison', 'reason': '金額系指標で多数のため、比較棒グラフが最適'}
            else:
                return {'type': 'comparison', 'reason': 'デフォルトとして比較棒グラフを選択'}

        except Exception as e:
            logger.error("複数指標グラフタイプ決定エラー: %s", e)
            return {'type': 'comparison', 'reason': 'エラーのため、フォールバック比較チャートを選択'}

    def create_chart_with_fallback(self, data, title="経済指標", chart_type_info=None):
        """
        時系列データ不足時の代替処理を含むチャート作成

        Args:
            data: 統計データ（単一または複数）
            title: チャートタイトル
            chart_type_info: グラフタイプ情報（determine_optimal_chart_typeの結果）

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # データが単一か複数かを判定
            if isinstance(data, list):
                is_single = len(data) == 1
                target_data = data[0] if is_single else data
            else:
                is_single = True
                target_data = data
                data = [data]  # リスト形式に統一

            # グラフタイプが指定されていない場合は自動判定
            if not chart_type_info:
                chart_type_info = self.determine_optimal_chart_type(data, single_indicator=is_single)

            chart_type = chart_type_info.get('type', 'simple_bar')
            reason = chart_type_info.get('reason', '自動選択')

            # グラフタイプ選択の理由をログ出力
            logger.info("選択されたグラフタイプ: %s", chart_type)
            logger.info("選択理由: %s", reason)

            # 履歴に記録
            self.chart_type_history.append({
                'timestamp': datetime.now(),
                'chart_type': chart_type,
                'reason': reason,
                'indicator_count': len(data)
            })

            # グラフタイプに応じてチャートを作成
            chart_path = None

            if chart_type == 'time_series':
                if is_single:
                    chart_path = self.create_single_indicator_time_series(target_data, title)
                else:
                    chart_path = self.create_time_series_chart(data, title)

            elif chart_type == 'gauge':
                chart_path = self.create_gauge_chart(target_data, title)

            elif chart_type == 'radar':
                chart_path = self.create_radar_chart(target_data, title)

            elif chart_type == 'donut':
                chart_path = self.create_donut_chart(target_data, title)

            elif chart_type == 'area':
                chart_path = self.create_area_chart(target_data, title)

            elif chart_type == 'enhanced_bar':
                chart_path = self.create_enhanced_bar_chart(target_data, title)

            elif chart_type == 'pie':
                if is_single:
                    chart_path = self.create_pie_chart(target_data, title)
                else:
                    chart_path = self.create_pie_chart(data, title)

            elif chart_type == 'comparison':
                chart_path = self.create_comparison_chart(data, title)

            elif chart_type == 'trend':
                chart_path = self.create_trend_analysis_chart(data, title)

            elif chart_type == 'comprehensive':
                chart_path = self.create_comprehensive_chart(data, title)

            else:  # 'simple_bar' or fallback
                chart_path = self.create_simple_bar_chart(target_data, title)

            # チャート作成に失敗した場合のフォールバック処理
            if not chart_path:
                logger.warning("メインチャート作成失敗。フォールバック処理を実行")
                chart_path = self._create_fallback_chart(target_data, title)

            return chart_path

        except Exception as e:
            logger.error("チャート作成エラー: %s", e)
            # 最終フォールバック
            return self._create_emergency_fallback_chart(target_data if 'target_data' in locals() else data, title)

    def _create_fallback_chart(self, data, title):
        """フォールバック用のシンプルなチャート作成"""
        try:
            logger.info("フォールバック処理: シンプルな棒グラフを作成")
            return self.create_simple_bar_chart(data, title)
        except Exception as e:
            logger.error("フォールバックチャート作成エラー: %s", e)
            return self._create_emergency_fallback_chart(data, title)

    def _create_emergency_fallback_chart(self, data, title):
        """緊急フォールバック: テキストベースの要約画像を作成"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.axis('off')

            # データの要約テキストを作成
            if isinstance(data, list):
                summary_text = "経済指標データ\n\n"
                for i, item in enumerate(data[:5]):  # 最大5個まで
                    name = item.get('name', 'Unknown')
                    value = item.get('value', 'N/A')
                    unit = item.get('unit', '')
                    summary_text += "%d. %s: %s%s\n" % (i+1, name, value, unit)
            else:
                name = data.get('name', 'Unknown')
                value = data.get('value', 'N/A')
                unit = data.get('unit', '')
                summary_text = "経済指標データ\n\n%s\n%s%s" % (name, value, unit)

            # テキストを表示
            ax.text(0.5, 0.5, summary_text, ha='center', va='center',
                   fontsize=16, fontweight='bold', transform=ax.transAxes,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))

            ax.set_title(title, fontsize=24, fontweight='bold', pad=20)

            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "emergency_fallback_%s.png" % timestamp
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("緊急フォールバックチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("緊急フォールバックチャート作成エラー: %s", e)
            return None

    def create_single_indicator_time_series(self, data, title="経済指標時系列分析"):
        """
        単一指標の時系列チャートを作成（直近5年分のデータ）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(16, 10))

            indicator_name = data.get('name', 'Unknown')
            unit = data.get('unit', '')

            logger.info("=== 時系列チャート生成開始 ===")
            logger.info("指標名: %s", indicator_name)

            # 履歴データから時系列を構築
            dates = []
            values = []
            historical_data = data.get('historical_data', [])

            logger.info("時系列データ件数: %d件", len(historical_data))

            if historical_data:
                for hist_item in historical_data:
                    date_str = hist_item.get('@time', '')
                    value = hist_item.get('$')

                    if date_str and value is not None:
                        try:
                            # 複数の日付フォーマットを試す
                            date_obj = None
                            for fmt in ['%Y%m%d', '%Y%m', '%Y', '%Y/%m/%d', '%Y-%m-%d']:
                                try:
                                    date_obj = datetime.strptime(str(date_str), fmt)
                                    break
                                except ValueError:
                                    continue

                            if date_obj:
                                dates.append(date_obj)
                                values.append(float(value))
                        except (ValueError, TypeError):
                            continue

            logger.info("有効な時系列データ: %d件", len(dates))

            # 現在の値を適切に追加（重複チェック付き）
            current_value = data.get('value')
            if current_value is not None:
                try:
                    current_val = float(current_value)

                    if dates:
                        # 既存データの最新日付を取得
                        latest_date = max(dates)

                        # 現在値が既存の最新値と異なる場合のみ追加
                        latest_value = values[dates.index(latest_date)]
                        if abs(current_val - latest_value) > 0.001:  # 微小な差は無視
                            # 月次データの場合は1ヶ月後、年次データの場合は1年後
                            try:
                                if len(dates) > 12:  # 月次データと推定
                                    if latest_date.month == 12:
                                        current_date = latest_date.replace(month=1, year=latest_date.year + 1)
                                    else:
                                        current_date = latest_date.replace(month=latest_date.month + 1)
                                else:  # 年次データと推定
                                    current_date = latest_date.replace(year=latest_date.year + 1)

                                dates.append(current_date)
                                values.append(current_val)
                                logger.info("現在値を追加: %s = %s", current_date.strftime('%Y/%m'), current_val)
                            except ValueError as e:
                                logger.warning("現在値の日付計算エラー: %s", e)
                    else:
                        # 履歴データがない場合は現在値のみ
                        dates = [datetime.now()]
                        values = [current_val]
                        logger.info("履歴データなし - 現在値のみ: %s", current_val)

                except (ValueError, TypeError) as e:
                    logger.warning("現在値の数値変換エラー: %s", e)

            # 時系列データが不足している場合のフォールバック処理
            if not dates or not values:
                logger.warning("時系列データが不足 - 現在値のみでチャート作成: %s", indicator_name)
                # 現在値のみでシンプルなチャートを作成
                current_value = data.get('value')
                if current_value is not None:
                    try:
                        val = float(current_value)
                        dates = [datetime.now()]
                        values = [val]
                        logger.info("現在値のみでチャート作成: %s = %s", indicator_name, val)
                    except (ValueError, TypeError):
                        logger.error("現在値の数値変換失敗: %s", current_value)
                        return None
                else:
                    logger.error("現在値も取得できません: %s", indicator_name)
                    return None

            # データの重複除去とソート
            if dates and values:
                # 日付と値のペアを作成し、日付でソート
                data_pairs = list(zip(dates, values))

                # 重複する日付を除去（最新の値を保持）
                unique_data = {}
                for date, value in data_pairs:
                    unique_data[date] = value

                # 日付でソート
                sorted_items = sorted(unique_data.items())
                dates, values = zip(*sorted_items) if sorted_items else ([], [])

                logger.info("重複除去後のデータ件数: %d件", len(dates))

                # 直近5年のデータのみを使用
                five_years_ago = datetime.now() - timedelta(days=5*365)
                recent_data = [(d, v) for d, v in zip(dates, values) if d >= five_years_ago]

                if recent_data:
                    dates, values = zip(*recent_data)
                    logger.info("直近5年のデータ件数: %d件", len(dates))
                else:
                    logger.warning("直近5年のデータが見つかりません")
            else:
                logger.warning("有効な時系列データがありません")

            # 折れ線グラフを作成（線を確実に表示）
            if len(dates) >= 2:
                # 複数データポイントがある場合は必ず線グラフ
                logger.info("=== 折れ線グラフ描画開始 ===")
                logger.info("データポイント数: %d", len(dates))
                logger.info("日付範囲: %s ～ %s", dates[0], dates[-1])
                logger.info("値の範囲: %.2f ～ %.2f", min(values), max(values))

                # 線グラフを明示的に描画
                line = ax.plot(dates, values,
                              linestyle='-',        # 実線を明示
                              marker='o',           # 円形マーカー
                              linewidth=4,          # 太い線
                              markersize=8,         # 大きなマーカー
                              color=self.colors['primary'],
                              alpha=1.0,            # 完全不透明
                              markerfacecolor='white',
                              markeredgecolor=self.colors['primary'],
                              markeredgewidth=3,
                              label="%s" % (indicator_name,),
                              zorder=10)            # 最前面に表示

                logger.info("折れ線グラフ描画完了: %d個のデータポイント", len(dates))

                # 線が描画されたことを確認
                if line:
                    logger.info("線オブジェクト作成成功: %s", type(line[0]))
                else:
                    logger.error("線オブジェクト作成失敗")

            elif len(dates) == 1:
                # 単一データポイントの場合は大きなマーカーで表示
                ax.scatter(dates, values, s=200, color=self.colors['primary'], alpha=1.0,
                          edgecolors='white', linewidth=3, label="%s" % (indicator_name,),
                          zorder=10)
                logger.info("単一ポイント表示: %d個のデータポイント", len(dates))
            else:
                logger.error("データポイントが0個です")

            # トレンドラインを追加
            if len(values) >= 3:
                # 線形回帰でトレンドを計算
                x_numeric = [(d - dates[0]).days for d in dates]
                z = np.polyfit(x_numeric, values, 1)
                p = np.poly1d(z)
                trend_values = [p(x) for x in x_numeric]

                ax.plot(dates, trend_values, '--', linewidth=3, alpha=0.7,
                       color=self.colors['accent'], label='トレンド')

            # 最新値を強調
            if dates and values:
                latest_date, latest_value = dates[-1], values[-1]
                ax.scatter([latest_date], [latest_value], s=200, color=self.colors['success'],
                          zorder=5, label='最新値: %.2f' % latest_value)

                # 最新値にラベルを追加
                ax.annotate('%.2f' % latest_value,
                           (latest_date, latest_value),
                           textcoords="offset points", xytext=(10,10),
                           ha='left', fontsize=18, fontweight='bold',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=18, fontweight='bold', pad=15)
            ax.set_xlabel('年月', fontsize=14, fontweight='bold')
            ax.set_ylabel('%s (%s)' % (indicator_name, unit), fontsize=14, fontweight='bold')

            # 日付軸の設定（短縮フォーマット）
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
            plt.xticks(rotation=45, fontsize=11, ha='right')
            plt.yticks(fontsize=11)

            # 凡例（コンパクトに）
            ax.legend(loc='upper left', fontsize=10, framealpha=0.9)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--')

            # Y軸の範囲を調整（データの変動を見やすくする）
            if values:
                y_min, y_max = min(values), max(values)
                y_range = y_max - y_min
                if y_range > 0:
                    ax.set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.15, left=0.12, right=0.95, top=0.9)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "timeseries_single_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("単一指標時系列チャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("単一指標時系列チャート作成エラー: %s", e)
            plt.close()
            return None

    def create_gauge_chart(self, data, title="経済指標"):
        """
        ゲージチャート（率系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            # ゲージの設定
            val = float(value)
            max_val = 100 if '%' in unit or '率' in indicator_name else max(val * 1.5, 100)

            # 角度計算（半円ゲージ）
            theta = np.linspace(0, np.pi, 100)
            r = np.ones_like(theta)

            # 背景ゲージ
            ax.plot(theta, r, color='lightgray', linewidth=20, alpha=0.3)

            # 値のゲージ
            value_theta = np.linspace(0, np.pi * (val / max_val), int(100 * val / max_val))
            value_r = np.ones_like(value_theta)

            # 色の決定
            if val <= max_val * 0.3:
                color = self.colors['success']
            elif val <= max_val * 0.7:
                color = self.colors['warning']
            else:
                color = self.colors['danger']

            ax.plot(value_theta, value_r, color=color, linewidth=20, alpha=0.8)

            # 針の描画
            needle_angle = np.pi * (val / max_val)
            ax.plot([needle_angle, needle_angle], [0, 0.8], color='black', linewidth=4)

            # 値の表示
            ax.text(np.pi/2, 0.5, '%s%s' % (val, unit), ha='center', va='center',
                   fontsize=32, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor='white', alpha=0.9))

            # 目盛りの追加
            for i in range(0, int(max_val) + 1, int(max_val/5)):
                angle = np.pi * (i / max_val)
                ax.plot([angle, angle], [0.9, 1.0], color='black', linewidth=2)
                ax.text(angle, 1.1, str(i), ha='center', va='center', fontsize=12)

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=60)
            ax.set_ylim(0, 1.2)
            ax.set_theta_zero_location('W')
            ax.set_theta_direction(1)
            ax.set_thetagrids([])
            ax.set_rgrids([])
            ax.spines['polar'].set_visible(False)

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "gauge_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("ゲージチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("ゲージチャート作成エラー: %s", e)
            plt.close()
            return None

    def create_pie_chart_single(self, data, title="経済指標"):
        """
        円グラフ（消費・支出系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 10))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # 比較値の設定（過去平均や参考値など）
            reference_value = val * 0.8  # 参考値
            remaining = max(0, reference_value - val) if val < reference_value else val * 0.2

            # データの準備
            if val >= reference_value:
                labels = ['現在値\n%s%s' % (val, unit), '上昇分\n%.1f%s' % (val - reference_value, unit)]
                sizes = [reference_value, val - reference_value]
                colors = [self.colors['primary'], self.colors['success']]
            else:
                labels = ['現在値\n%s%s' % (val, unit), '参考値との差\n%.1f%s' % (remaining, unit)]
                sizes = [val, remaining]
                colors = [self.colors['primary'], self.colors['secondary']]

            # 円グラフの作成
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                             startangle=90, textprops={'fontsize': 16, 'fontweight': 'bold'})

            # 中央に値を表示
            ax.text(0, 0, '%s%s' % (val, unit), ha='center', va='center',
                   fontsize=28, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "gauge_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("円グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("円グラフ作成エラー: %s", e)
            plt.close()
            return None

    def create_radar_chart(self, data, title="経済指標"):
        """
        レーダーチャート（物価・指数系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # レーダーチャートの軸（5つの評価軸を作成）
            categories = ['現在値', '前月比', '前年比', '平均値', '最高値']
            N = len(categories)

            # 角度の計算
            angles = [n / float(N) * 2 * np.pi for n in range(N)]
            angles += angles[:1]  # 円を閉じる

            # 値の正規化（動的スケール）
            max_scale = max(val * 1.2, 100)  # 現在値の1.2倍または100のうち大きい方
            values = [
                min(val / max_scale * 100, 100),  # 現在値
                75,  # 前月比（仮値）
                80,  # 前年比（仮値）
                70,  # 平均値（仮値）
                90   # 最高値（仮値）
            ]
            values += values[:1]  # 円を閉じる

            # レーダーチャートの描画
            ax.plot(angles, values, 'o-', linewidth=3, color=self.colors['primary'], alpha=0.8)
            ax.fill(angles, values, alpha=0.25, color=self.colors['primary'])

            # 軸ラベルの設定
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(categories, fontsize=14, fontweight='bold')

            # 値の範囲設定
            ax.set_ylim(0, 100)
            ax.set_yticks([20, 40, 60, 80, 100])
            ax.set_yticklabels(['20', '40', '60', '80', '100'], fontsize=12)
            ax.grid(True)

            # 中央に現在値を表示
            ax.text(0, 0, '%s%s' % (val, unit), ha='center', va='center',
                   fontsize=24, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=60)

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "radar_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("レーダーチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("レーダーチャート作成エラー: %s", e)
            plt.close()
            return None

    def create_donut_chart(self, data, title="経済指標"):
        """
        ドーナツチャート（貿易・輸出入系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 10))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # ドーナツチャート用のデータ準備
            total = val * 1.3  # 全体を130%として設定
            remaining = total - val

            sizes = [val, remaining]
            labels = ['%s\n%s%s' % (indicator_name, val, unit), 'その他\n%.1f%s' % (remaining, unit)]
            colors = [self.colors['primary'], self.colors['secondary']]

            # ドーナツチャートの作成
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                             startangle=90, pctdistance=0.85,
                                             textprops={'fontsize': 14, 'fontweight': 'bold'})

            # 中央の穴を作成
            centre_circle = plt.Circle((0,0), 0.70, fc='white')
            fig.gca().add_artist(centre_circle)

            # 中央に値を表示
            ax.text(0, 0, '%s%s' % (val, unit), ha='center', va='center',
                   fontsize=32, fontweight='bold', color=self.colors['primary'])

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "donut_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("ドーナツチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("ドーナツチャート作成エラー: %s", e)
            plt.close()
            return None

    def create_area_chart(self, data, title="経済指標"):
        """
        エリアチャート（指数・建設系指標に最適）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(14, 8))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')
            historical_data = data.get('historical_data', [])

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # 時系列データがある場合は使用、ない場合は仮想データ生成
            if len(historical_data) >= 3:
                dates = []
                values = []
                for hist_item in historical_data[-12:]:  # 直近12ヶ月
                    date_str = hist_item.get('@time', '')
                    hist_value = hist_item.get('$')

                    if date_str and hist_value is not None:
                        try:
                            date_obj = datetime.strptime(str(date_str), '%Y%m')
                            dates.append(date_obj)
                            values.append(float(hist_value))
                        except:
                            continue

                # 現在値を追加
                if dates:
                    latest_date = max(dates)
                    next_month = latest_date.replace(month=latest_date.month + 1 if latest_date.month < 12 else 1,
                                                   year=latest_date.year + (1 if latest_date.month == 12 else 0))
                    dates.append(next_month)
                    values.append(val)
            else:
                # 仮想データ生成（過去6ヶ月）
                dates = []
                values = []
                base_date = datetime.now()

                for i in range(6, 0, -1):
                    month_ago = base_date.replace(month=base_date.month - i if base_date.month > i else base_date.month - i + 12,
                                                year=base_date.year - (1 if base_date.month <= i else 0))
                    dates.append(month_ago)
                    # 現在値の周辺でランダムな変動を生成
                    variation = val * (0.9 + 0.2 * np.random.random())
                    values.append(variation)

                dates.append(base_date)
                values.append(val)

            if not dates or not values:
                logger.warning("エリアチャート用のデータが不足: %s", indicator_name)
                return None

            # エリアチャートの作成
            ax.fill_between(dates, values, alpha=0.6, color=self.colors['primary'], label=indicator_name)
            ax.plot(dates, values, color=self.colors['primary'], linewidth=3, marker='o', markersize=6)

            # 最新値を強調
            if dates and values:
                latest_date, latest_value = dates[-1], values[-1]
                ax.scatter([latest_date], [latest_value], s=200, color=self.colors['accent'],
                          zorder=5, edgecolors='white', linewidth=2)

                # 最新値ラベル
                ax.annotate('%.1f%s' % (latest_value, unit),
                           (latest_date, latest_value),
                           textcoords="offset points", xytext=(10,10),
                           ha='left', fontsize=18, fontweight='bold',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)
            ax.set_ylabel('%s (%s)' % (indicator_name, unit), fontsize=12, fontweight='bold')
            ax.set_xlabel('期間', fontsize=12, fontweight='bold')

            # 日付軸の設定（短縮フォーマット）
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m'))
            plt.xticks(rotation=45, fontsize=10, ha='right')
            plt.yticks(fontsize=10)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--')

            # 凡例（コンパクトに）
            ax.legend(loc='upper left', fontsize=9, framealpha=0.9)

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.15, left=0.12, right=0.95, top=0.9)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "area_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("エリアチャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("エリアチャート作成エラー: %s", e)
            plt.close()
            return None

    def create_enhanced_bar_chart(self, data, title="経済指標"):
        """
        時系列データを活用した改良チャート（意味不明な比較項目棒グラフを廃止）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            # 時系列データがある場合は時系列チャートを作成
            historical_data = data.get('historical_data', [])
            if len(historical_data) >= 3:
                logger.info("時系列データ利用可能 - 時系列チャートに変更: %s", data.get('name', 'Unknown'))
                return self.create_single_indicator_time_series(data, title)

            # 時系列データが不足している場合はシンプルな現在値表示
            fig, ax = plt.subplots(figsize=(12, 8))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            val = float(value)

            # シンプルな現在値表示（意味不明な比較項目は削除）
            categories = ['現在値']
            values = [val]

            # 色の設定
            colors = [self.colors['primary']]  # 現在値のみ

            # シンプルな棒グラフの作成
            bars = ax.bar(categories, values, color=colors, alpha=0.8,
                         edgecolor='white', linewidth=3)

            # 値をバーの上に表示
            for bar, val_item in zip(bars, values):
                height = bar.get_height()
                label = '%.1f%s' % (val_item, unit)
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                       label, ha='center', va='bottom',
                       fontweight='bold', fontsize=24,
                       bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.8))

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=40)
            ax.set_ylabel('%s (%s)' % (indicator_name, unit), fontsize=22, fontweight='bold')
            ax.set_xlabel('', fontsize=22, fontweight='bold')  # X軸ラベルを削除

            plt.xticks(rotation=45, ha='right', fontsize=16)
            plt.yticks(fontsize=18)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')

            # 凡例
            ax.legend(loc='upper right', fontsize=14)

            # Y軸の範囲調整
            y_min = min(values) * 0.9
            y_max = max(values) * 1.1
            ax.set_ylim(y_min, y_max)

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "enhanced_bar_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("強化棒グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("強化棒グラフ作成エラー: %s", e)
            plt.close()
            return None

    def create_single_indicator_comparison(self, data, title="経済指標比較"):
        """
        単一指標の比較チャート（正常範囲との比較）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(12, 8))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')
            normal_range = data.get('normal_range', [])

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            # 比較データの準備
            categories = ['現在値']
            values = [float(value)]
            colors = [self.colors['primary']]

            # 正常範囲がある場合は追加
            if normal_range and len(normal_range) >= 2:
                categories.extend(['正常範囲下限', '正常範囲上限'])
                values.extend([normal_range[0], normal_range[1]])
                colors.extend([self.colors['info'], self.colors['info']])

            # 棒グラフを作成
            bars = ax.bar(categories, values, color=colors, alpha=0.8, edgecolor='white', linewidth=2)

            # 値をバーの上に表示
            for bar, val in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                       '%.2f%s' % (val, unit), ha='center', va='bottom',
                       fontweight='bold', fontsize=16)

            # チャートの装飾
            ax.set_title(title, fontsize=48, fontweight='bold', pad=40)
            ax.set_ylabel('%s (%s)' % (indicator_name, unit), fontsize=22, fontweight='bold')

            plt.xticks(rotation=45, ha='right', fontsize=18)
            plt.yticks(fontsize=20)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')

            # レイアウト調整
            plt.tight_layout()

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "comparison_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            logger.info("比較チャート作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("比較チャート作成エラー: %s", e)
            plt.close()
            return None

    def create_simple_bar_chart(self, data, title="経済指標"):
        """
        シンプルな棒グラフ（最後のフォールバック）

        Args:
            data: 単一の統計データ
            title: チャートタイトル

        Returns:
            作成されたチャート画像のファイルパス
        """
        try:
            fig, ax = plt.subplots(figsize=(10, 6))

            indicator_name = data.get('name', 'Unknown')
            value = data.get('value')
            unit = data.get('unit', '')

            if value is None:
                logger.warning("値が不明です: %s", indicator_name)
                return None

            # シンプルな棒グラフ
            bar = ax.bar([indicator_name], [float(value)],
                        color=self.colors['primary'], alpha=0.8,
                        edgecolor='white', linewidth=2)

            # 値をバーの上に表示
            height = bar[0].get_height()
            ax.text(bar[0].get_x() + bar[0].get_width()/2., height + height*0.02,
                   '%s%s' % (value, unit), ha='center', va='bottom',
                   fontweight='bold', fontsize=20)

            # チャートの装飾（適切なサイズに調整）
            ax.set_title(title, fontsize=16, fontweight='bold', pad=15)
            ax.set_ylabel('値 (%s)' % unit, fontsize=12, fontweight='bold')

            plt.xticks(fontsize=11)
            plt.yticks(fontsize=11)

            # グリッド
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')

            # レイアウト調整（マージン確保）
            plt.subplots_adjust(bottom=0.15, left=0.12, right=0.95, top=0.9)

            # 保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = "simple_bar_%s.png" % (timestamp,)
            filepath = os.path.join(self.output_dir, filename)

            plt.savefig(filepath, dpi=150, facecolor='white', edgecolor='none')
            plt.close()

            logger.info("シンプル棒グラフ作成完了: %s", filepath)
            return filepath

        except Exception as e:
            logger.error("シンプル棒グラフ作成エラー: %s", e)
            plt.close()
            return None

    def cleanup_old_charts(self, max_age_hours=24):
        """
        古いチャートファイルを削除

        Args:
            max_age_hours: 保持する最大時間（時間）
        """
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=max_age_hours)

            for filename in os.listdir(self.output_dir):
                filepath = os.path.join(self.output_dir, filename)
                if os.path.isfile(filepath):
                    file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                    if file_time < cutoff_time:
                        os.remove(filepath)
                        logger.info("古いチャートファイルを削除: %s", filename)

        except Exception as e:
            logger.error("チャートファイルクリーンアップエラー: %s", e)
