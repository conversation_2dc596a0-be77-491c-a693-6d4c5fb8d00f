# Japan Economic Stats Twitter Bot

GitHub Actionsを活用した日本の経済統計データ自動分析・X投稿アプリケーション。e-Stat APIとGemini APIによる分析機能、実際のRSSニュース統合機能付き。

## 概要

このプロジェクトは、e-Stat APIから日本の主要経済指標データ（30+指標）を自動取得し、実際のRSSニュースフィードと組み合わせて、Google Gemini AIで分析した結果をX（旧Twitter）に自動投稿します。GitHub Actionsによる定期実行で、最新の経済動向を継続的に発信し、経済指標の変動を継続的に追跡・発信することを目的としています。

## 🆕 最新の改良内容（2025年6月版）

### 1. Twitter投稿文字数最適化
- **130-140文字制限**: Twitter投稿を130-140文字に最適化し、より簡潔で効果的な情報発信を実現
- **スマート切り詰め機能**: 長いテキストを自然な区切りで適切に短縮

### 2. 実際のRSSニュース統合
- **5つの主要ニュースソース**: NHK、Yahoo!、朝日新聞、毎日新聞、読売新聞からリアルタイムニュース取得
- **経済関連ニュース自動選別**: 経済統計と関連性の高いニュースを自動的に選定・分析に反映
- **HTMLクリーンアップ機能**: RSSフィードのHTMLタグを適切に除去してテキスト処理

### 3. e-Stat統計データセット大幅拡張
- **30+経済指標**: 従来の17指標から29指標に大幅拡張
- **新規追加指標**:
  - 第三次産業活動指数
  - 建設工事受注動態統計
  - 商業動態統計（卸売業販売額）
  - 特定サービス産業動態統計
  - 工業生産指数（季節調整済）
  - 企業物価指数（国内企業物価指数）
  - 全国百貨店売上高
  - コンビニエンスストア売上高
  - 自動車販売台数
  - 設備投資額（全産業）
  - 企業向けサービス価格指数
  - その他1指標

## 主要機能

### 📊 データ収集・分析
- **自動データ取得**: e-Stat APIから日本の30+経済指標データを定期的に取得
- **実際のRSSニュース統合**: 5つの主要ニュースソースからリアルタイム経済ニュースを取得
- **AI分析**: Google Gemini AIが経済データとニュースを統合分析し、重要なインサイトやトレンドを生成
- **異常値検出**: 統計的手法による経済指標の異常値・変動点の自動検出
- **時系列分析**: 経済指標の傾向分析と予測

### 🐦 投稿・配信
- **X自動投稿**: 分析結果を130-140文字に最適化してX（旧Twitter）に自動投稿
- **Slack通知**: 詳細な分析レポートをSlackに通知（オプション）
- **スマート文字数制限**: 自然な区切りでテキストを適切に短縮

### 🤖 自動化・運用
- **自動実行**: GitHub Actionsにより、毎日午前9時（JST）にデータ取得、分析、投稿の一連のワークフローが自動実行
- **結果の保存**: 分析結果は`results/`ディレクトリにJSON形式で保存され、過去の分析履歴を確認可能
- **エラーハンドリング**: 各APIの障害に対する適切なフォールバック機能

## 対象データ

### 📈 経済指標（29指標）

#### 雇用・労働
- 完全失業率
- 有効求人倍率
- 現金給与総額

#### 物価・インフレ
- 消費者物価指数（総合）
- 企業物価指数
- 企業物価指数（国内企業物価指数）
- 企業向けサービス価格指数

#### 生産・製造業
- 鉱工業生産指数
- 鉱工業出荷指数
- 鉱工業在庫指数
- 工業生産指数（季節調整済）

#### 消費・小売
- 家計消費支出
- 小売業販売額
- 全国百貨店売上高
- コンビニエンスストア売上高
- 自動車販売台数

#### 投資・企業活動
- 機械受注（船舶・電力を除く民需）
- 設備投資額（全産業）
- 法人企業統計（売上高）
- 法人企業統計（経常利益）

#### 建設・不動産
- 住宅着工戸数
- 建設工事受注動態統計

#### 貿易・国際
- 普通貿易統計（輸出額）
- 普通貿易統計（輸入額）

#### サービス業
- 第三次産業活動指数
- 特定サービス産業動態統計

#### 商業・流通
- 商業動態統計（卸売業販売額）

#### 景気指標
- 景気動向指数（先行指数）
- 景気動向指数（遅行指数）

### 📰 ニュースソース（5社）
- **NHKニュース経済**: 公共放送の信頼性の高い経済ニュース
- **Yahoo!ニュース経済**: 幅広い経済情報とトレンド
- **朝日新聞経済**: 詳細な経済分析記事
- **毎日新聞経済**: 多角的な経済報道
- **読売新聞経済**: 総合的な経済情報

## プロジェクト構造

```
japan-economic-stats-twitter-bot/
├── .github/
│   └── workflows/
│       └── daily-analysis.yml        # GitHub Actionsワークフロー定義
├── src/
│   ├── data_collector.py             # e-Stat APIからデータを取得（30+指標対応）
│   ├── news_collector.py             # RSSフィードからニュースを取得・分析
│   ├── gemini_analyzer.py            # Gemini AIによるデータ・ニュース統合分析
│   ├── twitter_poster.py             # X投稿（130-140文字最適化）
│   ├── slack_notifier.py             # Slack通知機能
│   └── main.py                       # メイン実行スクリプト
├── config/
│   └── indicators.json               # 経済指標設定（29指標定義）
├── results/                          # 分析結果保存ディレクトリ
├── requirements.txt                  # 依存ライブラリ（RSS解析ライブラリ追加）
├── .env                              # 環境変数設定ファイル
└── README.md
```

## セットアップ

### 1. 依存関係のインストール

プロジェクトのルートディレクトリで以下のコマンドを実行し、必要なPythonライブラリをインストールします。

```bash
pip install -r requirements.txt
```

### 2. 環境変数の設定

APIキーや認証情報などの機密情報は、`.env`ファイルに設定します。プロジェクトのルートディレクトリに`.env`ファイルを作成し、以下の変数を設定してください。

```env
# e-Stat API（必須）
ESTAT_API_KEY=your_estat_api_key_here

# Google Gemini API（必須）
GEMINI_API_KEY=your_gemini_api_key_here

# X (Twitter) API（必須）
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here
TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret_here

# Slack API（オプション）
SLACK_BOT_TOKEN=your_slack_bot_token_here
SLACK_CHANNEL=your_slack_channel_here
```

### 3. ローカル実行

以下のコマンドで、各コンポーネントを個別に、または統合して実行できます。

```bash
# 全体の実行（データ取得 -> ニュース取得 -> AI分析 -> X投稿 -> 結果保存）
python main.py

# 個別実行（開発・デバッグ用）
python src/data_collector.py      # e-Stat データ取得のみ
python src/news_collector.py      # RSSニュース取得のみ
python src/gemini_analyzer.py     # AI分析のみ
python src/twitter_poster.py      # X投稿のみ
python src/slack_notifier.py      # Slack通知のみ
```

## GitHub Actionsの設定

このアプリケーションはGitHub Actionsを利用して自動実行されます。**重要**: このプロジェクトでは`.env`ファイルから直接環境変数を読み込みます。

### 環境変数の設定

プロジェクトルートに`.env`ファイルを作成し、以下の環境変数を設定してください：

```env
# e-Stat API設定
ESTAT_API_KEY=your_estat_api_key_here

# Gemini API設定
GEMINI_API_KEY=your_gemini_api_key_here

# Twitter API設定
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here
TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret_here
```

### 設定手順

1. プロジェクトルートディレクトリに`.env`ファイルを作成
2. `.env.example`を参考に、各APIキーの実際の値を設定
3. **重要**: `.env`ファイルをGitリポジトリにコミット（APIキーを含むため）
4. GitHub Actionsは`.env`ファイルから環境変数を自動読み込み

### 注意事項

- **セキュリティ警告**: `.env`ファイルにはAPIキーが含まれるため、プライベートリポジトリでの使用を強く推奨します
- パブリックリポジトリの場合は、GitHubのシークレット機能の使用を検討してください
- ローカル開発とGitHub Actions両方で同じ`.env`ファイルを使用します
- `.env`ファイルの形式は`KEY=VALUE`（スペースなし）で設定してください

### 自動実行スケジュール

- **頻度**: 毎日午前9時（JST）に自動実行されます。
- **トリガー**: cronスケジュール（`0 0 * * *` UTC時間）による定期実行に加え、GitHub Actionsのワークフローページから手動で実行することも可能です。

## 技術スタック

- **言語**: Python 3.7+
- **外部API**:
  - e-Stat API（経済統計データ）
  - Google Gemini API（AI分析）
  - X (Twitter) API（投稿）
  - Slack API（通知、オプション）
- **RSS処理**: feedparser, BeautifulSoup4
- **CI/CD**: GitHub Actions
- **データ処理**: Pandas, NumPy
- **AI分析**: Google Gemini Pro
- **HTTP通信**: requests
- **環境管理**: python-dotenv

## ライセンス

MIT License

## 貢献

プルリクエストやイシューの報告を歓迎します。貢献に関する詳細は、[CONTRIBUTING.md](CONTRIBUTING.md)（※もしあれば）を参照してください。

## 🧪 テスト状況

### 実施済みテスト
- ✅ **RSSフィード取得テスト**: 5社のニュースソースから正常にデータ取得
- ✅ **e-Stat API接続テスト**: 29の経済指標設定が正常に動作
- ✅ **文字数制限テスト**: Twitter投稿が130-140文字に適切に最適化
- ✅ **統合テスト**: 全コンポーネントが正常に連携動作
- ✅ **エラーハンドリングテスト**: API障害時のフォールバック機能

### テスト結果
```
総合結果: 5/5 テスト通過 🎉
- RSSニュース収集: ✅ PASS
- e-Stat設定ファイル: ✅ PASS
- ニュース関連性分析: ✅ PASS
- 全体ワークフロー: ✅ PASS
```

## 🚀 今後の展望

### 短期的な改善計画
- **多言語対応**: 英語での経済分析投稿機能
- **グラフ生成**: 経済指標の可視化機能
- **アラート機能**: 重要な経済変動の即座通知

### 長期的な発展計画
- **機械学習予測**: 経済指標の予測モデル統合
- **インタラクティブ機能**: ユーザーからの質問に自動回答
- **国際比較**: 他国経済指標との比較分析

## 参考リンク

- [e-Stat API Documentation](https://www.e-stat.go.jp/api/)
- [e-Stat 統計データベース一覧](https://www.e-stat.go.jp/stat-search/database?page=1)
- [Google Gemini API Documentation](https://ai.google.dev/gemini-api/docs)
- [X API Documentation](https://developer.twitter.com/en/docs)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)

## アーキテクチャ

```mermaid
graph TB
    subgraph "GitHub Actions"
        A[Daily Cron Job<br/>毎日自動実行]
    end

    subgraph "Main Application"
        B[main.py<br/>EconomicStatsBot]
    end

    subgraph "Data Collection Layer"
        C[data_collector.py<br/>EStatDataCollector]
        D[news_collector.py<br/>NewsCollector]
    end

    subgraph "Analysis Layer"
        E[gemini_analyzer.py<br/>GeminiAnalyzer]
    end

    subgraph "Output Layer"
        F[twitter_poster.py<br/>TwitterPoster]
        G[slack_notifier.py<br/>SlackNotifier]
    end

    subgraph "External APIs"
        H[e-Stat API<br/>30+ 経済統計指標]
        I[RSS Feeds<br/>5つのニュースソース]
        J[Google Gemini API<br/>AI分析・要約]
        K[X Twitter API<br/>投稿・配信]
        L[Slack API<br/>通知]
    end

    subgraph "Configuration"
        M[config/indicators.json<br/>統計指標設定]
        N[.env<br/>環境変数・APIキー]
    end

    subgraph "Output"
        O[results/<br/>分析結果保存]
        P[Twitter投稿<br/>130-140文字制限]
        Q[Slack通知<br/>詳細レポート]
    end

    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G

    C --> H
    D --> I
    E --> J
    F --> K
    G --> L

    M --> C
    N --> B

    B --> O
    F --> P
    G --> Q
```

### アーキテクチャの特徴

#### 🏗️ レイヤー構造
- **データ収集層**: e-Stat API（経済統計）とRSSフィード（ニュース）から並行してデータを取得
- **分析層**: Gemini AIが経済データとニュースを統合分析
- **出力層**: Twitter（簡潔な投稿）とSlack（詳細レポート）への配信

#### 🔄 データフロー
1. **データ収集**: 30+経済指標 + 5社のRSSニュース
2. **関連性分析**: ニュースと経済指標の関連性を自動判定
3. **AI統合分析**: Gemini AIが全データを統合して分析
4. **最適化配信**: 各プラットフォームに最適化された形式で配信

#### 🛡️ 信頼性設計
- **フォールバック機能**: 各APIの障害時に代替手段を提供
- **エラーハンドリング**: 部分的な障害でも処理を継続
- **データ保存**: 全ての分析結果を永続化して履歴管理


